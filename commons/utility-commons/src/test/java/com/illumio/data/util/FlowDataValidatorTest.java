package com.illumio.data.util;

import com.illumio.data.util.FlowDataValidator.ValidationResult;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for FlowDataValidator.
 */
class FlowDataValidatorTest {

    @Test
    void testValidateNullJson() {
        ValidationResult result = FlowDataValidator.validate((String) null);
        assertFalse(result.isValid());
        assertEquals("INVALID_JSON", result.getErrorCode());
        assertEquals("JSON string is null or empty", result.getDetailMessage());
    }

    @Test
    void testValidateEmptyJson() {
        ValidationResult result = FlowDataValidator.validate("");
        assertFalse(result.isValid());
        assertEquals("INVALID_JSON", result.getErrorCode());
        assertEquals("JSON string is null or empty", result.getDetailMessage());
    }

    @Test
    void testValidateInvalidJson() {
        ValidationResult result = FlowDataValidator.validate("{invalid json");
        assertFalse(result.isValid());
        assertEquals("INVALID_JSON_FORMAT", result.getErrorCode());
        assertTrue(result.getDetailMessage().startsWith("Invalid JSON format:"));
    }

    @Test
    void testValidateJsonArray() {
        ValidationResult result = FlowDataValidator.validate("[]");
        assertFalse(result.isValid());
        assertEquals("INVALID_JSON_TYPE", result.getErrorCode());
        assertEquals("JSON must be an object", result.getDetailMessage());
    }

    @Test
    void testValidateMissingRequiredFields() {
        String json = "{}";
        ValidationResult result = FlowDataValidator.validate(json);
        assertFalse(result.isValid());
        assertTrue(result.getErrorCode().startsWith("MISS_REQUIRED_FIELD_"));
        assertTrue(result.getDetailMessage().startsWith("Missing required field:"));
    }

    @Test
    void testValidateUnrecognizedField() {
        String json = """
            {
                "SrcIP": "***********",
                "DestIP": "********",
                "Port": 80,
                "Proto": "TCP",
                "SentBytes": 1024,
                "ReceivedBytes": 2048,
                "IllumioTenantId": "tenant-123",
                "StartTime": "2023-01-01T10:00:00Z",
                "EndTime": "2023-01-01T10:05:00Z",
                "UnknownField": "some value"
            }
            """;

        ValidationResult result = FlowDataValidator.validate(json);
        assertFalse(result.isValid());
        assertEquals("UNRECOGNIZED_FIELD_UNKNOWNFIELD", result.getErrorCode());
        assertEquals("Unrecognized field: UnknownField", result.getDetailMessage());
    }

    @Test
    void testValidateValidMinimalJson() {
        String json = """
            {
                "SrcIP": "***********",
                "DestIP": "********",
                "Port": 80,
                "Proto": "TCP",
                "SentBytes": 1024,
                "ReceivedBytes": 2048,
                "IllumioTenantId": "tenant-123",
                "StartTime": "2023-01-01T10:00:00Z",
                "EndTime": "2023-01-01T10:05:00Z"
            }
            """;

        ValidationResult result = FlowDataValidator.validate(json);
        assertTrue(result.isValid(), "Expected valid JSON but got error: " + result.getDetailMessage());
        assertNull(result.getErrorCode());
        assertNull(result.getDetailMessage());
    }

    @Test
    void testValidateCompleteValidJson() {
        String json = """
            {
                "SrcIP": "***********",
                "SrcId": "src-123",
                "CSSrcId": "cs-src-123",
                "DestIP": "********",
                "DestId": "dest-123",
                "CSDestId": "cs-dest-123",
                "Port": 80,
                "Proto": "TCP",
                "SentBytes": 1024,
                "ReceivedBytes": 2048,
                "IllumioTenantId": "tenant-123",
                "SrcTenantId": "src-tenant-123",
                "SrcSubId": "src-sub-123",
                "SrcRegion": "us-east-1",
                "SrcResId": "src-res-123",
                "SrcVnetId": "src-vnet-123",
                "SrcUserName": "user1",
                "DestTenantId": "dest-tenant-123",
                "DestSubId": "dest-sub-123",
                "DestRegion": "us-west-1",
                "DestResId": "dest-res-123",
                "DestVnetId": "dest-vnet-123",
                "DestUserName": "user2",
                "SrcFlowType": "internal",
                "DestFlowType": "external",
                "SrcThreatLevel": 1,
                "DestThreatLevel": 2,
                "SrcIsWellknown": true,
                "DestIsWellknown": false,
                "SrcDomain": "example.com",
                "DestDomain": "target.com",
                "SrcCountry": "US",
                "DestCountry": "CA",
                "SrcCity": "New York",
                "DestCity": "Toronto",
                "SrcCloudProvider": "AWS",
                "DestCloudProvider": "Azure",
                "MaliciousIPLatitude": 40.7128,
                "MaliciousIPLongitude": -74.0060,
                "FlowCount": 5,
                "PacketsReceived": 10,
                "PacketsSent": 8,
                "StartTime": "2023-01-01T10:00:00.123456Z",
                "EndTime": "2023-01-01T10:05:00.654321Z"
            }
            """;
        
        ValidationResult result = FlowDataValidator.validate(json);
        assertTrue(result.isValid(), "Expected valid JSON but got error: " + result.getDetailMessage());
        assertNull(result.getErrorCode());
        assertNull(result.getDetailMessage());
    }

    @Test
    void testValidateInvalidStringField() {
        String json = """
            {
                "SrcIP": 123,
                "DestIP": "********",
                "Port": 80,
                "Proto": "TCP",
                "SentBytes": 1024,
                "ReceivedBytes": 2048,
                "IllumioTenantId": "tenant-123",
                "StartTime": "2023-01-01T10:00:00Z",
                "EndTime": "2023-01-01T10:05:00Z"
            }
            """;
        
        ValidationResult result = FlowDataValidator.validate(json);
        assertFalse(result.isValid());
        assertEquals("WRONG_TYPE_SRCIP", result.getErrorCode());
        assertEquals("Field 'SrcIP' must be a string", result.getDetailMessage());
    }

    @Test
    void testValidateInvalidIntegerField() {
        String json = """
            {
                "SrcIP": "***********",
                "DestIP": "********",
                "Port": "eighty",
                "Proto": "TCP",
                "SentBytes": 1024,
                "ReceivedBytes": 2048,
                "IllumioTenantId": "tenant-123",
                "StartTime": "2023-01-01T10:00:00Z",
                "EndTime": "2023-01-01T10:05:00Z"
            }
            """;
        
        ValidationResult result = FlowDataValidator.validate(json);
        assertFalse(result.isValid());
        assertEquals("WRONG_TYPE_PORT", result.getErrorCode());
        assertEquals("Field 'Port' must be an integer", result.getDetailMessage());
    }

    @Test
    void testValidateInvalidLongField() {
        String json = """
            {
                "SrcIP": "***********",
                "DestIP": "********",
                "Port": 80,
                "Proto": "TCP",
                "SentBytes": "not-a-number",
                "ReceivedBytes": 2048,
                "IllumioTenantId": "tenant-123",
                "StartTime": "2023-01-01T10:00:00Z",
                "EndTime": "2023-01-01T10:05:00Z"
            }
            """;
        
        ValidationResult result = FlowDataValidator.validate(json);
        assertFalse(result.isValid());
        assertEquals("WRONG_TYPE_SENTBYTES", result.getErrorCode());
        assertEquals("Field 'SentBytes' must be a long", result.getDetailMessage());
    }

    @Test
    void testValidateInvalidBooleanField() {
        String json = """
            {
                "SrcIP": "***********",
                "DestIP": "********",
                "Port": 80,
                "Proto": "TCP",
                "SentBytes": 1024,
                "ReceivedBytes": 2048,
                "IllumioTenantId": "tenant-123",
                "SrcIsWellknown": "yes",
                "StartTime": "2023-01-01T10:00:00Z",
                "EndTime": "2023-01-01T10:05:00Z"
            }
            """;
        
        ValidationResult result = FlowDataValidator.validate(json);
        assertFalse(result.isValid());
        assertEquals("WRONG_TYPE_SRCISWELLKNOWN", result.getErrorCode());
        assertEquals("Field 'SrcIsWellknown' must be a boolean", result.getDetailMessage());
    }

    @Test
    void testValidateInvalidDoubleField() {
        String json = """
            {
                "SrcIP": "***********",
                "DestIP": "********",
                "Port": 80,
                "Proto": "TCP",
                "SentBytes": 1024,
                "ReceivedBytes": 2048,
                "IllumioTenantId": "tenant-123",
                "MaliciousIPLatitude": "not-a-number",
                "StartTime": "2023-01-01T10:00:00Z",
                "EndTime": "2023-01-01T10:05:00Z"
            }
            """;
        
        ValidationResult result = FlowDataValidator.validate(json);
        assertFalse(result.isValid());
        assertEquals("WRONG_TYPE_MALICIOUSIPLATITUDE", result.getErrorCode());
        assertEquals("Field 'MaliciousIPLatitude' must be a number", result.getDetailMessage());
    }

    @Test
    void testValidateInvalidDateTimeField() {
        String json = """
            {
                "SrcIP": "***********",
                "DestIP": "********",
                "Port": 80,
                "Proto": "TCP",
                "SentBytes": 1024,
                "ReceivedBytes": 2048,
                "IllumioTenantId": "tenant-123",
                "StartTime": "invalid-date",
                "EndTime": "2023-01-01T10:05:00Z"
            }
            """;
        
        ValidationResult result = FlowDataValidator.validate(json);
        assertFalse(result.isValid());
        assertEquals("WRONG_TYPE_STARTTIME", result.getErrorCode());
        assertEquals("Field 'StartTime' has invalid datetime format", result.getDetailMessage());
    }

    @Test
    void testValidateVariousDateTimeFormats() {
        String[] validDateTimes = {
            "2025-07-18T19:42:07.0000000Z",  // 7 decimal places - new format
            "2023-01-01T10:00:00.123456Z",   // 6 decimal places
            "2023-01-01T10:00:00.123Z",      // 3 decimal places
            "2023-01-01T10:00:00Z",          // No decimal places
            "2023-01-01T10:00:00",           // No timezone
            "2023-01-01 10:00:00.1234567",   // 7 decimal places, space-separated
            "2023-01-01 10:00:00.123456",    // 6 decimal places, space-separated
            "2023-01-01 10:00:00.123",       // 3 decimal places, space-separated
            "2023-01-01 10:00:00"            // No decimal places, space-separated
        };
        
        for (String dateTime : validDateTimes) {
            String json = String.format("""
                {
                    "SrcIP": "***********",
                    "DestIP": "********",
                    "Port": 80,
                    "Proto": "TCP",
                    "SentBytes": 1024,
                    "ReceivedBytes": 2048,
                    "IllumioTenantId": "tenant-123",
                    "StartTime": "%s",
                    "EndTime": "%s"
                }
                """, dateTime, dateTime);
            
            ValidationResult result = FlowDataValidator.validate(json);
            assertTrue(result.isValid(),
                "Expected valid datetime format '" + dateTime + "' but got error: " + result.getDetailMessage());
        }
    }

    @Test
    void testValidateWithNullOptionalFields() {
        String json = """
            {
                "SrcIP": "***********",
                "SrcId": null,
                "DestIP": "********",
                "DestId": null,
                "Port": 80,
                "Proto": "TCP",
                "SentBytes": 1024,
                "ReceivedBytes": 2048,
                "IllumioTenantId": "tenant-123",
                "StartTime": "2023-01-01T10:00:00Z",
                "EndTime": "2023-01-01T10:05:00Z"
            }
            """;
        
        ValidationResult result = FlowDataValidator.validate(json);
        assertTrue(result.isValid(), "Expected valid JSON with null optional fields but got error: " + result.getDetailMessage());
        assertNull(result.getErrorCode());
        assertNull(result.getDetailMessage());
    }

    @Test
    void testValidateSevenDecimalPlacesDateTime() {
        String json = """
            {
                "SrcIP": "***********",
                "DestIP": "********",
                "Port": 80,
                "Proto": "TCP",
                "SentBytes": 1024,
                "ReceivedBytes": 2048,
                "IllumioTenantId": "tenant-123",
                "StartTime": "2025-07-18T19:42:07.0000000Z",
                "EndTime": "2025-07-18T19:47:07.1234567Z"
            }
            """;

        ValidationResult result = FlowDataValidator.validate(json);
        assertTrue(result.isValid(), "Expected valid JSON with 7 decimal places datetime but got error: " + result.getDetailMessage());
        assertNull(result.getErrorCode());
        assertNull(result.getDetailMessage());
    }
}
