syntax = "proto2";

/* northbound api version 2 */
package Illumio;
option java_package = "com.illumio.data.model";
option java_multiple_files = true;

message TrafficDataRecord {
    required int64 timestamp = 1 [default = -1];
    required bytes src_ip = 2;
    required bytes dst_ip = 3;
    required int32 dst_port = 4 [default = -1];
    required int32 proto = 5 [default = -1];
    required int32 count = 6 [default = -1];
    optional int32 icmp_type = 7 [default = -1];
    optional int32 icmp_code = 8 [default = -1];
    optional bytes src_mac = 9;
    optional bytes dst_mac = 10;
    optional bytes device_addr = 11;
    optional int32 src_mask_bits = 12;
    optional int32 dst_mask_bits = 13;
    optional uint64 octets = 14;
}

message TrafficData {
    required int32 aggregation_seconds = 1 [default = -1];
    optional int32 org_id = 2;
    optional bytes installation_id = 3;
    optional bytes pce_host = 4;
    repeated TrafficDataRecord flows = 10;
}
