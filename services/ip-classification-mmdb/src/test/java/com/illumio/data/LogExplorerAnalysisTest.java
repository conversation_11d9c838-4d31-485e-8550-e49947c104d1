package com.illumio.data;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit test to analyze log-explorer-1757545258388.ndjson file:
 * 1. Parse JSON entries (should be 8)
 * 2. Extract unique pod names
 * 3. Deduplicate "Assigned partitions" for each line
 * 4. Aggregate all pods and output total unique pods and partitions
 */
class LogExplorerAnalysisTest {

    private static final String RESOURCE_FILE = "log-explorer-1757545258388.ndjson";
    private static final Pattern PARTITION_PATTERN = Pattern.compile("\\[([^\\]]+)\\]");

    @Test
    @SneakyThrows
    void testLogExplorerAnalysis() {
        ObjectMapper objectMapper = new ObjectMapper();
        
        // Read and parse the NDJSON file
        List<JsonNode> logEntries = readNdjsonFile(objectMapper);
        
        // Verify we have exactly 8 entries
        assertEquals(8, logEntries.size(), "Should have exactly 8 log entries");
        
        // Extract pod names and partitions
        Set<String> uniquePodNames = new HashSet<>();
        Set<String> allUniquePartitions = new HashSet<>();
        Map<String, Set<String>> podToPartitions = new HashMap<>();
        
        for (JsonNode entry : logEntries) {
            // Extract pod name
            String podName = entry.get("podName").asText();
            uniquePodNames.add(podName);
            
            // Extract log message and parse partitions
            String logMessage = entry.get("log").asText();
            JsonNode logJson = objectMapper.readTree(logMessage);
            String message = logJson.get("message").asText();
            
            // Extract partitions from the message
            Set<String> partitions = extractPartitions(message);
            podToPartitions.put(podName, partitions);
            allUniquePartitions.addAll(partitions);
        }
        
        // Verify unique pod names (should be 8)
        assertEquals(8, uniquePodNames.size(), "Should have 8 unique pod names");
        
        // Verify pod names follow expected pattern
        for (String podName : uniquePodNames) {
            assertTrue(podName.startsWith("prod-ip-classification-mmdb-d8576d54-"), 
                      "Pod name should follow expected pattern: " + podName);
        }
        
        // Print analysis results
        System.out.println("=== Log Explorer Analysis Results ===");
        System.out.println("Total log entries parsed: " + logEntries.size());
        System.out.println("Total unique pods: " + uniquePodNames.size());
        System.out.println("Total unique partitions: " + allUniquePartitions.size());
        
        System.out.println("\nUnique Pod Names:");
        uniquePodNames.stream().sorted().forEach(pod -> System.out.println("  - " + pod));
        
        System.out.println("\nPartitions per Pod:");
        podToPartitions.entrySet().stream()
            .sorted(Map.Entry.comparingByKey())
            .forEach(entry -> {
                System.out.println("  " + entry.getKey() + ": " + entry.getValue().size() + " partitions");
                entry.getValue().stream()
                    .sorted((p1, p2) -> {
                        // Extract numeric suffix and sort by it
                        int num1 = Integer.parseInt(p1.substring("decorated-flow-v1-".length()));
                        int num2 = Integer.parseInt(p2.substring("decorated-flow-v1-".length()));
                        return Integer.compare(num1, num2);
                    })
                    .forEach(partition -> System.out.println("    - " + partition));
            });
        
        System.out.println("\nAll Unique Partitions (" + allUniquePartitions.size() + " total):");
        allUniquePartitions.stream()
            .sorted((p1, p2) -> {
                // Extract numeric suffix and sort by it
                int num1 = Integer.parseInt(p1.substring("decorated-flow-v1-".length()));
                int num2 = Integer.parseInt(p2.substring("decorated-flow-v1-".length()));
                return Integer.compare(num1, num2);
            })
            .forEach(partition -> System.out.println("  - " + partition));
        
        // Verify each pod has exactly 8 partitions assigned
        for (Map.Entry<String, Set<String>> entry : podToPartitions.entrySet()) {
            assertEquals(8, entry.getValue().size(), 
                        "Pod " + entry.getKey() + " should have exactly 8 partitions assigned");
        }
        
        // Verify we have the expected total number of unique partitions
        // Since each pod gets 8 partitions and we have 8 pods, we expect 64 total partitions
        // (assuming no overlap, which we'll verify)
        assertEquals(64, allUniquePartitions.size(), 
                    "Should have 64 unique partitions total (8 pods × 8 partitions each)");
        
        // Verify partition naming pattern
        for (String partition : allUniquePartitions) {
            assertTrue(partition.startsWith("decorated-flow-v1-"), 
                      "Partition should follow expected pattern: " + partition);
        }
        
        // Verify partition numbers are in expected range (0-63)
        Set<Integer> partitionNumbers = allUniquePartitions.stream()
            .map(partition -> Integer.parseInt(partition.substring("decorated-flow-v1-".length())))
            .collect(Collectors.toSet());
        
        assertEquals(64, partitionNumbers.size(), "Should have 64 unique partition numbers");
        assertEquals(0, Collections.min(partitionNumbers), "Minimum partition number should be 0");
        assertEquals(63, Collections.max(partitionNumbers), "Maximum partition number should be 63");
    }
    
    @SneakyThrows
    private List<JsonNode> readNdjsonFile(ObjectMapper objectMapper) {
        List<JsonNode> entries = new ArrayList<>();
        
        try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream(RESOURCE_FILE);
             BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
            
            String line;
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                if (!line.isEmpty()) {
                    JsonNode entry = objectMapper.readTree(line);
                    entries.add(entry);
                }
            }
        }
        
        return entries;
    }
    
    private Set<String> extractPartitions(String message) {
        Set<String> partitions = new HashSet<>();
        
        // Extract content within square brackets from the message
        Matcher matcher = PARTITION_PATTERN.matcher(message);
        if (matcher.find()) {
            String partitionList = matcher.group(1);
            // Split by comma and clean up each partition name
            String[] partitionArray = partitionList.split(",");
            for (String partition : partitionArray) {
                partitions.add(partition.trim());
            }
        }
        
        return partitions;
    }
}
