package com.illumio.data.configuration;

import lombok.RequiredArgsConstructor;

import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderOptions;

import java.util.HashMap;
import java.util.Map;

@Configuration
@RequiredArgsConstructor
public class KafkaSenderConfiguration {
    private final LabelRecommendationConfig labelRecommendationConfig;

    @Bean
    public KafkaSender<String, String> kafkaSender() {
        Map<String, Object> producerProps = new HashMap<>();
        producerProps.put(
                ProducerConfig.BOOTSTRAP_SERVERS_CONFIG,
                labelRecommendationConfig.getKafkaCommonConfig().getBootstrapServers());
        producerProps.put(
                ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG,
                StringSerializer.class);
        producerProps.put(
                ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG,
                StringSerializer.class);
        producerProps.put(ProducerConfig.MAX_REQUEST_SIZE_CONFIG,
                labelRecommendationConfig.getKafkaSenderConfig().getMaxRequestSize());
        producerProps.put(ProducerConfig.REQUEST_TIMEOUT_MS_CONFIG,
                labelRecommendationConfig.getKafkaSenderConfig().getRequestTimeoutMs());
        producerProps.put(ProducerConfig.METADATA_MAX_IDLE_CONFIG,
                labelRecommendationConfig.getKafkaSenderConfig().getMetadataMaxIdleMs());
        producerProps.put(ProducerConfig.DELIVERY_TIMEOUT_MS_CONFIG,
                labelRecommendationConfig.getKafkaSenderConfig().getDeliveryTimeoutMs());
        producerProps.put(ProducerConfig.BATCH_SIZE_CONFIG, labelRecommendationConfig.getKafkaSenderConfig().getBatchSizeKb() * 1024);
        producerProps.put(ProducerConfig.LINGER_MS_CONFIG, labelRecommendationConfig.getKafkaSenderConfig().getLingerMs());
        producerProps.put(ProducerConfig.BUFFER_MEMORY_CONFIG, labelRecommendationConfig.getKafkaSenderConfig().getBufferMemoryMb() * 1024 * 1024);
        if (null != labelRecommendationConfig.getKafkaCommonConfig().getIsSasl()
                && labelRecommendationConfig.getKafkaCommonConfig().getIsSasl()) {
            producerProps.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_SSL");
            producerProps.put(SaslConfigs.SASL_MECHANISM, "PLAIN");
            producerProps.put(
                    SaslConfigs.SASL_JAAS_CONFIG,
                    labelRecommendationConfig.getKafkaCommonConfig().getSaslJaasConfig());
        }
        producerProps.put(CommonClientConfigs.METADATA_MAX_AGE_CONFIG,
                labelRecommendationConfig.getKafkaCommonConfig().getMetadataMaxAgeMs());
        producerProps.put(CommonClientConfigs.CONNECTIONS_MAX_IDLE_MS_CONFIG,
                labelRecommendationConfig.getKafkaCommonConfig().getConnectionMaxIdleMs());

        SenderOptions<String, String> senderOptions =
                SenderOptions.<String, String>create(producerProps)
                        .stopOnError(false)
                        // Non-blocking back-pressure
                        .maxInFlight(1024);

        return KafkaSender.create(senderOptions);
    }
}
