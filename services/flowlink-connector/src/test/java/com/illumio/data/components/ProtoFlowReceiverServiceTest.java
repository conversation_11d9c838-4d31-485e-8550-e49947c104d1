package com.illumio.data.components;

import com.illumio.data.model.CefRecord;
import com.illumio.data.model.TrafficData;
import com.illumio.data.model.TrafficDataRecord;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.kafka.receiver.KafkaReceiver;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.InetAddress;
import java.time.Duration;
import java.util.List;
import java.util.zip.GZIPOutputStream;
import com.google.protobuf.ByteString;
import reactor.kafka.receiver.ReceiverRecord;

import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ProtoFlowReceiverServiceTest {

    @Mock
    private KafkaReceiver<String, byte[]> kafkaReceiver;
    @Mock
    private CefFlowSenderService cefFlowSenderService;
    private ProtoFlowReceiverService protoFlowReceiverService;

    @BeforeEach
    void setUp() {
        protoFlowReceiverService = new ProtoFlowReceiverService(kafkaReceiver, cefFlowSenderService);
    }

    @Test
    void testConsumeFlows_ShouldDecompressAndSend() throws IOException {
        byte[] gzippedData = createGzippedTrafficData();

        final ReceiverRecord<String, byte[]> record = mock(ReceiverRecord.class);
        when(record.key()).thenReturn("tenant-123");
        when(record.value()).thenReturn(gzippedData);

        when(kafkaReceiver.receiveAutoAck()).thenReturn(Flux.just(Flux.just(record)));

        when(cefFlowSenderService.sendFlows(any()))
                .thenAnswer(invocation -> {
                    Flux<?> inputFlux = invocation.getArgument(0);
                    return inputFlux.then();
                });

        protoFlowReceiverService.consumeFlows();

        ArgumentCaptor<Flux<CefRecord>> fluxCaptor = ArgumentCaptor.forClass((Class) Flux.class);
        await().atMost(Duration.ofSeconds(5))
                .untilAsserted(() -> {
                    verify(cefFlowSenderService, times(1)).sendFlows(fluxCaptor.capture());

                    Flux<CefRecord> capturedFlux = fluxCaptor.getValue();
                    List<CefRecord> flowRecords = capturedFlux.collectList().block();

                    String expectedFlow1 = "CEF:0|Illumio|Flowlink|1.0|100|Traffic Flow|0|src=************ dst=******** dvc=*************** dpt=443 proto=6 cnt=11 smac=aa:bb:cc:dd:ee:ff dmac=ff:ee:dd:cc:bb:aa smask=32 dmask=24 bytes=6945 rule_action=Accept conn_direction=Internal start=1757717671000 end=1757717731000 cs1Label=OrgId cs1=12345 cs2Label=InstallationId cs2=datacenter-1 cs3Label=PceHost cs3=devtest4.ilabs.io:8443 cs4Label=AggregationSeconds cs4=60 cs5Label=IcmpType cs5=8 cs6Label=IcmpCode cs6=0";
                    String expectedFlow2 = "CEF:0|Illumio|Flowlink|1.0|100|Traffic Flow|0|src=************ dst=******** dvc=*************** dpt=443 proto=6 cnt=11 smac=aa:bb:cc:dd:ee:ff dmac=ff:ee:dd:cc:bb:aa smask=32 dmask=24 bytes=6945 rule_action=Accept conn_direction=Internal start=1757717671000 end=1757717731000 cs1Label=OrgId cs1=12345 cs2Label=InstallationId cs2=datacenter-1 cs3Label=PceHost cs3=devtest4.ilabs.io:8443 cs4Label=AggregationSeconds cs4=60 cs5Label=IcmpType cs5=8 cs6Label=IcmpCode cs6=0";

                    // Normalize flows by removing dynamic rt field
                    List<String> normalizedFlows = flowRecords.stream()
                            .map(flow -> flow.getCefMessage().replaceAll(" rt=\\d+", ""))
                            .toList();

                    assertThat(normalizedFlows).containsExactly(expectedFlow1, expectedFlow2);
                    assertThat(flowRecords.stream().allMatch(flow -> flow.getOrgId().equals("12345"))).isTrue();
                });
    }


    public static byte[] createGzippedTrafficData() throws IOException {
        TrafficDataRecord record = TrafficDataRecord.newBuilder()
                .setTimestamp(1757717671L)
                .setSrcIp(byteStringFromIp("************"))
                .setDstIp(byteStringFromIp("********"))
                .setDeviceAddr(byteStringFromIp("***************"))
                .setDstPort(443)
                .setProto(6) // TCP
                .setCount(11)
                .setSrcMac(ByteString.copyFromUtf8("aa:bb:cc:dd:ee:ff"))
                .setDstMac(ByteString.copyFromUtf8("ff:ee:dd:cc:bb:aa"))
                .setIcmpType(8)
                .setIcmpCode(0)
                .setSrcMaskBits(32)
                .setDstMaskBits(24)
                .setOctets(6945)
                .build();

        TrafficData trafficData = TrafficData.newBuilder()
                .setOrgId(12345)
                .setPceHost(ByteString.copyFromUtf8("devtest4.ilabs.io:8443"))
                .setInstallationId(ByteString.copyFromUtf8("datacenter-1"))
                .setAggregationSeconds(60)
                .addFlows(record)
                .addFlows(record.toBuilder().setDstIp(byteStringFromIp("********")).build()) // second flow
                .build();

        byte[] protoBytes = trafficData.toByteArray();

        return gzip(protoBytes);
    }

    private static byte[] gzip(byte[] data) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try (GZIPOutputStream gzipOut = new GZIPOutputStream(baos)) {
            gzipOut.write(data);
        }
        return baos.toByteArray();
    }

    @SneakyThrows
    private static ByteString byteStringFromIp(String ip) {
        return ByteString.copyFrom(InetAddress.getByName(ip).getAddress());
    }
}
