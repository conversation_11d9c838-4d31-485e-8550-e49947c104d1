package com.illumio.data.model.constants;

import lombok.Getter;

import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

@Getter
public enum Fields {
    SERVICE("string", "service", "Service", "Service"),
    SEVERITY("number", "severity", "Severity", "Severity"),
    SOURCE_IP("string", "source_ip", "Source IP", "SrcIP"),
    DESTINATION_IP("string", "destination_ip", "Destination IP", "DestIP"),
    IP("string", "ip", "IP", "Ip") {
        @Override
        public Fields resolve(TrafficDirection direction) {
            return switch (direction) {
                case INBOUND -> SOURCE_IP;
                case OUTBOUND -> DESTINATION_IP;
                // Added for backward compatibility
                case BOTH -> this;
            };
        }
    },
    PORT("number", "port", "Port", "Port"),
    PROTOCOL("string", "protocol", "Protocol", "Proto"),
    FLOWS_TREND("string", "flows_trend", "Flows", "FlowTrend"),
    PREVIOUS_FLOWS("number", "prev_flows", "Previous Flows", "FlowCount1"),
    AGGREGATED_FLOWS("number", "flows", "Flows", "AggFlowCount"),
    PREVIOUS_AGGREGATED_FLOWS("number", "prev_flows", "Previous Flows", "AggFlowCount1"),
    BYTES_TREND("string", "bytes_trend", "Bytes", "ByteTrend"),
    AGGREGATED_BYTES("number", "bytes", "Bytes", "AggByteCount"),
    PREVIOUS_AGGREGATED_BYTES("number", "prev_bytes", "Previous Bytes", "AggByteCount1"),
    SOURCE_ZONE("string", "source_zone", "Source Zone", "SourceZone"),
    DESTINATION_ZONE("string", "destination_zone", "Destination Zone", "DestinationZone"),
    SOURCE_ROLE("string", "source_role", "Source Role", "SourceLabel"),
    DESTINATION_ROLE("string", "destination_role", "Destination Role", "DestinationLabel"),
    ROLE("string", "role", "Role", "Role") {
        @Override
        public Fields resolve(TrafficDirection direction) {
            return switch (direction) {
                case INBOUND -> DESTINATION_ROLE;
                case OUTBOUND -> SOURCE_ROLE;
                // Added for backward compatibility
                case BOTH -> this;
            };
        }
    },
    SENT_BYTES("number", "sent_bytes", "Sent Bytes", "SentBytes"),
    RECEIVED_BYTES("number", "received_bytes", "Received Bytes", "ReceivedBytes"),
    SOURCE_TENANT_ID("string", "source_tenant", "Source Tenant Id", "SrcTenantId"),
    DESTINATION_TENANT_ID("string", "destination_tenant", "Destination Tenant Id", "DestTenantId"),
    SUBSCRIPTION_ID("string", "subscription_id", "Subscription Id", "SubId"),
    SOURCE_SUBSCRIPTION_ID("string", "source_subscription", "Source Subscription Id", "SrcSubId"),
    DESTINATION_SUBSCRIPTION_ID("string", "destination_subscription", "Destination Subscription Id", "DestSubId"),
    SOURCE_REGION("string", "source_region", "Source Region", "SrcRegion"),
    DESTINATION_REGION("string", "destination_region", "Destination Region", "DestRegion"),
    SOURCE_RESOURCE_ID("string", "source_resource_id", "Source Resource Id", "SrcResId"),
    DESTINATION_RESOURCE_ID("string", "destination_resource_id", "Destination Resource Id", "DestResId"),
    SOURCE_RESOURCE_NAME("string", "source_resource_name", "Source Resource Name", "SrcResourceName"),
    DESTINATION_RESOURCE_NAME("string", "dest_resource_name", "Destination Resource Name", "DestResourceName"),
    SOURCE_RESOURCE_CATEGORY("string", "source_resource_cat", "Source Resource Category", "SrcResourceCategory"),
    DESTINATION_RESOURCE_CATEGORY("string", "dest_resource_cat", "Destination Resource Category", "DestResourceCategory"),
    SOURCE_RESOURCE_TYPE("string", "source_resource_type", "Source Resource Type", "SrcResourceType"),
    DESTINATION_RESOURCE_TYPE("string", "dest_resource_type", "Destination Resource Type", "DestResourceType"),
    SOURCE_VNET_ID("string", "source_vnet", "Source Vnet Id", "SrcVnetId"),
    DESTINATION_VNET_ID("string", "destination_vnet", "Destination Vnet Id", "DestVnetId"),
    SOURCE_CLOUD_TAGS("string", "source_cloud_tags", "Source Cloud Tags", "SrcCloudTags"),
    DESTINATION_CLOUD_TAGS("string", "destination_cloud_tags", "Destination Cloud Tags", "DestCloudTags"),
    TRAFFIC_STATUS("string", "traffic_status", "Traffic Status", "TrafficStatus"),
    PACKETS_SENT("number", "packets_sent", "Packets Sent", "PacketsSent"),
    PACKETS_RECEIVED("number", "packets_received", "Packets Received", "PacketsReceived"),
    SOURCE_THREAT_LEVEL("string", "source_threat_level", "Source Threat Level", "SrcThreatLevel"),
    DESTINATION_THREAT_LEVEL("string", "destination_threat_level", "Destination Threat Level", "DestThreatLevel"),
    THREAT_LEVEL("string", "threat_level", "Threat Level", "ThreatLevel"),
    PREVIOUS_THREAT_LEVEL("string", "previous_threat_level", "Previous Threat Level", "ThreatLevel1"),
    SOURCE_WELL_KNOWN("string", "source_well_known", "Source Well Known", "SrcIsWellknown"),
    DESTINATION_WELL_KNOWN("string", "destination_well_known", "Destination Well Known", "DestIsWellknown"),
    SOURCE_DOMAIN("string", "source_domain", "Source Domain", "SrcDomain"),
    DESTINATION_DOMAIN("string", "destination_domain", "Destination Domain", "DestDomain"),
    SOURCE_COUNTRY("string", "source_country", "Source Country", "SrcCountry"),
    COUNTRY("string", "country", "Country", "Country"),
    DESTINATION_COUNTRY("string", "destination_country", "Destination Country", "DestCountry"),
    SOURCE_CITY("string", "source_city", "Source City", "SrcCity"),
    DESTINATION_CITY("string", "destination_city", "Destination City", "DestCity"),
    START_TIME("timestamp", "start_time", "Start Time", "StartTime"),
    END_TIME("timestamp", "end_time", "End Time", "EndTime"),
    COUNT("number", "count", "Count", "Count"),
    PREVIOUS_COUNT("number", "previous_count", "Previous Count", "Count1"),
    TOTAL_ROWS("number","total_rows","Total Rows","totalRows"),
    AGGREGATE_FIELD("string", "aggregate_field", "Aggregate Field", "AggregateField"),
    TIME_SERIES("string", "time_series", "Time Series", "TimeSeries"),
    COMPOUND_SERVICE("string", "compound_service", "Compound Service", "CompoundService"),
    TRAFFIC_DIRECTION("string", "traffic_direction", "Traffic Direction", "TrafficDirection"),
    CATEGORY("string", "category", "Category", "Category"),
    CATEGORY_ID("string", "category_id", "Category Id", "CategoryId"),
    SOURCE_ACCOUNT_NAME("string", "source_account_name", "Source Account Name", "SrcAccountName"),
    DESTINATION_ACCOUNT_NAME("string", "destination_account_name", "Destination Account Name", "DestAccountName"),
    ACCOUNT_NAME("string", "account_name", "Account Name", "AccountName"),
    CLOUD_PROVIDER("string", "cloud_provider", "Cloud Provider", "CloudProvider"),
    DESTINATION_EXTERNAL_LABEL("string","destination_external_label","Destination External Label","DestinationExternalLabel"),
    DESTINATION_EXTERNAL_LABEL_CATEGORY("string","destination_category","Destination Category","DestinationExternalLabelCategory"),
    LLM("string","llm", "LLM", "DestinationExternalLabel"),
    SOURCE_EXTERNAL_LABEL("string","source_external_label","Source External Label","SourceExternalLabel"),
    SOURCE_EXTERNAL_LABEL_CATEGORY("string","source_category","Source Category","SourceExternalLabelCategory"),
    SOURCE_CLOUD_PROVIDER("string","source_cloud_provider","Source Cloud Provider","SourceZone"),
    DESTINATION_CLOUD_PROVIDER("string","destination_cloud_provider","Destination Cloud Provider","DestinationZone"),
    RESOURCE_CATEGORY("string", "resource_category", "Resource Category", "ResourceCategory") {
        @Override
        public Fields resolve(TrafficDirection direction) {
            return switch (direction) {
                case INBOUND -> DESTINATION_RESOURCE_CATEGORY;
                case OUTBOUND -> SOURCE_RESOURCE_CATEGORY;
                // Added for backward compatibility
                case BOTH -> this;
            };
        }
    },
    RESOURCE_TYPE("string", "resource_type", "Resource Type", "ResourceType") {
        @Override
        public Fields resolve(TrafficDirection direction) {
            return switch (direction) {
                case INBOUND -> DESTINATION_RESOURCE_TYPE;
                case OUTBOUND -> SOURCE_RESOURCE_TYPE;
                // Added for backward compatibility
                case BOTH -> this;
            };
        }
    },
    RESOURCE_NAME("string", "resource_name", "Resource Name", "ResourceName") {
        @Override
        public Fields resolve(TrafficDirection direction) {
            return switch (direction) {
                case INBOUND -> DESTINATION_RESOURCE_NAME;
                case OUTBOUND -> SOURCE_RESOURCE_NAME;
                // Added for backward compatibility
                case BOTH -> this;
            };
        }
    },
    RESOURCE_ID("string", "resource_id", "Resource Id", "ResourceId") {
        @Override
        public Fields resolve(TrafficDirection direction) {
            return switch (direction) {
                case INBOUND -> DESTINATION_RESOURCE_ID;
                case OUTBOUND -> SOURCE_RESOURCE_ID;
                // Added for backward compatibility
                case BOTH -> this;
            };
        }
    },
    DOMAIN_NAME("string", "domain_name", "Domain Name", "DomainName"),
    SOURCE_DIRECTION("string", "source", "Source", "source"),
    DESTINATION_DIRECTION("string", "destination", "Destination", "destination"),
    ZONE("string", "zone", "Zone", "Zone") {
        @Override
        public Fields resolve(TrafficDirection direction) {
            return switch (direction) {
                case INBOUND -> DESTINATION_ZONE;
                case OUTBOUND -> SOURCE_ZONE;
                // Added for backward compatibility
                case BOTH -> this;
            };
        }
    },
    SERVICE_NAME("string", "service_name", "Service Name", "ServiceName"),
    REGION("string", "region", "Region", "Region") {
        @Override
        public Fields resolve(TrafficDirection direction) {
            return switch (direction) {
                case INBOUND -> DESTINATION_REGION;
                case OUTBOUND -> SOURCE_REGION;
                // Added for backward compatibility
                case BOTH -> this;
            };
        }
    };

    private final String fieldType;
    private final String fieldKey;
    private final String fieldDisplayName;
    private final String tableColumnName;
    private static final Map<String, String> fieldKeyToTableColumnNameMap = new HashMap<>();
    @Getter
    private static final Map<String, Map<String, Fields>> directionBoundFields  = new HashMap<>();
    @Getter
    private static final Set<String> fieldKeySet = new HashSet<>();

    static {
        for (Fields field : Fields.values()) {
            fieldKeyToTableColumnNameMap.put(field.getFieldKey(), field.getTableColumnName());
            fieldKeySet.add(field.getFieldKey());
        }

        directionBoundFields.put("INBOUND", Map.ofEntries(
              Map.entry ("IP", SOURCE_IP),
              Map.entry ("THREATLEVEL", SOURCE_THREAT_LEVEL),
              Map.entry ("COUNTERTHREATLEVEL", DESTINATION_THREAT_LEVEL),
              Map.entry ("COUNTRY", SOURCE_COUNTRY),
              Map.entry ("RESOURCEID", SOURCE_RESOURCE_ID),
              Map.entry ("COUNTERRESOURCEID", DESTINATION_RESOURCE_ID),
              Map.entry ("ROLE", DESTINATION_ROLE),
              Map.entry ("PORT", PORT),
              Map.entry ("SUBSCRIPTIONS", DESTINATION_SUBSCRIPTION_ID),
              Map.entry ("TENANTS", DESTINATION_TENANT_ID),
              Map.entry(  "ACCOUNTNAME", DESTINATION_ACCOUNT_NAME),
              Map.entry(  "CLOUDPROVIDER", DESTINATION_ZONE),
              Map.entry("RESOURCE_CATEGORY", SOURCE_RESOURCE_CATEGORY),
              Map.entry("RESOURCE_NAME", SOURCE_RESOURCE_NAME),
              Map.entry("DIRECTION", DESTINATION_DIRECTION)
        ));

        directionBoundFields.put("OUTBOUND", Map.ofEntries(
                Map.entry("IP", DESTINATION_IP),
                Map.entry("THREATLEVEL", DESTINATION_THREAT_LEVEL),
                Map.entry("COUNTERTHREATLEVEL", SOURCE_THREAT_LEVEL),
                Map.entry("COUNTRY", DESTINATION_COUNTRY),
                Map.entry("RESOURCEID", DESTINATION_RESOURCE_ID),
                Map.entry("COUNTERRESOURCEID", SOURCE_RESOURCE_ID),
                Map.entry("ROLE", SOURCE_ROLE),
                Map.entry("PORT", PORT),
                Map.entry("SUBSCRIPTIONS", SOURCE_SUBSCRIPTION_ID),
                Map.entry("TENANTS", SOURCE_TENANT_ID),
                Map.entry("ACCOUNTNAME", SOURCE_ACCOUNT_NAME),
                Map.entry(  "CLOUDPROVIDER", SOURCE_ZONE),
                Map.entry("RESOURCE_CATEGORY", DESTINATION_RESOURCE_CATEGORY),
                Map.entry("RESOURCE_NAME", DESTINATION_RESOURCE_NAME),
                Map.entry("DIRECTION", SOURCE_DIRECTION)
        ));
    }

    Fields(String fieldType, String fieldKey, String fieldDisplayName, String tableColumnName) {
        this.fieldType = fieldType;
        this.fieldKey = fieldKey;
        this.fieldDisplayName = fieldDisplayName;
        this.tableColumnName = tableColumnName;
    }

    // Return logical field if traffic direction - inbound/outbound is provided, otherwise returns self
    public Fields resolve(TrafficDirection direction) {
        return this;
    }

    public static String getTableColumnNameByFieldKey(String fieldKey) {
        return fieldKeyToTableColumnNameMap.get(fieldKey);
    }

    public static Fields getFieldByTableColumnName(String tableColumnName) {
        return Arrays.stream(Fields.values())
                .filter(field -> field.getTableColumnName().equals(tableColumnName))
                .findFirst()
                .orElse(null);
    }

    public static Fields getFieldByFieldKey(String fieldKey) {
        return Arrays.stream(Fields.values())
                .filter(field -> field.getFieldKey().equalsIgnoreCase(fieldKey))
                .findFirst()
                .orElse(null);
    }
}

