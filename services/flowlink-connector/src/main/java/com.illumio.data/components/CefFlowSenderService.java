package com.illumio.data.components;

import com.illumio.data.annotations.retry.RetryReactiveOnError;
import com.illumio.data.configuration.FlowlinkConnectorConfig;
import com.illumio.data.model.CefRecord;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.header.internals.RecordHeader;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderRecord;

@Slf4j
@Service
@RequiredArgsConstructor
public class CefFlowSenderService {

    private final KafkaSender<String, String> kafkaJsonFlowSender;
    private final FlowlinkConnectorConfig flowlinkConnectorConfig;

    private final static String ILLUMIO_TENANT_ID = "IllumioTenantId";

    @RetryReactiveOnError
    public Mono<Void> sendFlows(final Flux<CefRecord> flowCefs) {
        return kafkaJsonFlowSender.send(flowCefs.map(this::createProducerRecord))
                .doOnComplete(() ->
                        log.debug("Successfully pushed flows to Kafka"))
                .then();
    }

    @SneakyThrows
    private SenderRecord<String, String, String> createProducerRecord(final CefRecord flowCefRecord) {
        final String kafkaTopic = flowlinkConnectorConfig.getKafkaFlowCefProducerConfig().getTopic();
        final ProducerRecord<String, String> producerRecord = new ProducerRecord<>(kafkaTopic, null, flowCefRecord.getCefMessage());
        producerRecord.headers().add(new RecordHeader(ILLUMIO_TENANT_ID, flowCefRecord.getOrgId().getBytes()));
        return SenderRecord.create(producerRecord, null);
    }
}
