package com.illumio.data;

import com.illumio.data.components.IpClassificationFlowWriter;
import com.illumio.data.components.IpClassificationLookup;
import com.illumio.data.components.ResourceIdFlowReader;
import com.illumio.data.configuration.IpClassificationMmdbConfig;
import com.illumio.data.util.MetricsUtil;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Sinks;
import reactor.core.scheduler.Schedulers;
import reactor.kafka.receiver.KafkaReceiver;
import reactor.kafka.receiver.ReceiverRecord;
import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderRecord;
import reactor.kafka.sender.SenderResult;
import reactor.test.StepVerifier;

import java.time.Duration;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class IpClassificationMmdbPipelineReactorTest {

    @Mock
    private KafkaReceiver<String, String> kafkaReceiver;
    
    @Mock
    private KafkaSender<String, String> kafkaSender;
    
    @Mock
    private ResourceIdFlowReader resourceIdFlowReader;
    
    @Mock
    private IpClassificationLookup ipClassificationLookup;
    
    @Mock
    private IpClassificationFlowWriter ipClassificationFlowWriter;
    
    @Mock
    private IpClassificationMmdbConfig config;
    
    @Mock
    private MetricsUtil metricsUtil;

    private IpClassificationMmdbPipeline pipeline;

    @BeforeEach
    void setUp() {
        // Mock configuration - only mock what's actually used
        IpClassificationMmdbConfig.KafkaSenderConfig senderConfig = mock(IpClassificationMmdbConfig.KafkaSenderConfig.class);
        when(senderConfig.getSinkTopic()).thenReturn("test-sink-topic");
        when(config.getKafkaSenderConfig()).thenReturn(senderConfig);

        pipeline = new IpClassificationMmdbPipeline(
                kafkaReceiver,
                List.of(kafkaSender),
                resourceIdFlowReader,
                ipClassificationLookup,
                ipClassificationFlowWriter,
                config,
                Schedulers.boundedElastic(),
                metricsUtil
        );
    }

    @Test
    void testPipelineConstruction() {
        // Test that the pipeline initializes with separate receiver and sender components
        assertNotNull(pipeline);

        // Verify that the pipeline can be constructed without errors
        assertDoesNotThrow(() -> {
            // The pipeline should initialize without throwing exceptions
            pipeline.stop(); // Clean up
        });
    }

    @Test
    void testProcessConsumerRecord() {
        // Mock the processing chain
        ConsumerRecord<String, String> consumerRecord = new ConsumerRecord<>(
                "test-topic", 0, 0L, "test-key", "test-value"
        );

        when(resourceIdFlowReader.readTree(anyString())).thenReturn(Mono.just(mock(com.fasterxml.jackson.databind.JsonNode.class)));
        when(ipClassificationLookup.maybeAddIpClassification(any())).thenReturn(Mono.just(mock(com.fasterxml.jackson.databind.JsonNode.class)));
        when(ipClassificationFlowWriter.writeTreeAsString(any())).thenReturn(Mono.just("processed-value"));

        // Test the processing method
        StepVerifier.create(pipeline.processConsumerRecord(consumerRecord))
                .assertNext(senderRecord -> {
                    assertNotNull(senderRecord);
                    // SenderRecord doesn't expose ProducerRecord directly, but we can verify it was created
                    assertNotNull(senderRecord.correlationMetadata());
                    assertEquals("test-topic-0@0", senderRecord.correlationMetadata());
                })
                .verifyComplete();
    }
}
