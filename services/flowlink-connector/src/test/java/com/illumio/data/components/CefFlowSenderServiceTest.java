package com.illumio.data.components;

import com.illumio.data.configuration.FlowlinkConnectorConfig;
import com.illumio.data.model.CefRecord;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderRecord;
import reactor.kafka.sender.SenderResult;
import reactor.test.StepVerifier;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CefFlowSenderServiceTest {

    @Mock
    private KafkaSender<String, String> kafkaJsonFlowSender;
    @Mock
    private FlowlinkConnectorConfig flowlinkConnectorConfig;
    @Mock
    private FlowlinkConnectorConfig.KafkaProducerConfig kafkaProducerConfig;

    private CefFlowSenderService cefFlowSenderService;

    @BeforeEach
    void setUp() {
        when(flowlinkConnectorConfig.getKafkaFlowCefProducerConfig()).thenReturn(kafkaProducerConfig);
        when(kafkaProducerConfig.getTopic()).thenReturn("syslog-raw-cef-v1");

        cefFlowSenderService = new CefFlowSenderService(kafkaJsonFlowSender, flowlinkConnectorConfig);
    }

    @Test
    void testSendFlows_ShouldSendAllCefsToKafka() {
        // Arrange
        List<CefRecord> cefRecords = List.of(
                CefRecord.builder().orgId("12345").cefMessage("CEF_FLOW_1").build(),
                CefRecord.builder().orgId("12345").cefMessage("CEF_FLOW_2").build()
        );

        Flux<CefRecord> flowFlux = Flux.fromIterable(cefRecords);

        // Mock KafkaSender.send() to return a Flux of SenderResult
        when(kafkaJsonFlowSender.send(any(Flux.class)))
                .thenAnswer(invocation -> {
                    Flux<SenderRecord<String, String, String>> records = invocation.getArgument(0);
                    return records.map(record -> mock(SenderResult.class));
                });

        // Act
        Mono<Void> result = cefFlowSenderService.sendFlows(flowFlux);

        // Assert using StepVerifier
        StepVerifier.create(result)
                .verifyComplete();

        // Capture the Flux sent to KafkaSender
        ArgumentCaptor<Flux<SenderRecord<String, String, String>>> captor = ArgumentCaptor.forClass((Class) Flux.class);
        verify(kafkaJsonFlowSender, times(1)).send(captor.capture());

        List<SenderRecord<String, String, String>> capturedRecords = captor.getValue().collectList().block();

        // Verify each record contains correct topic and value
        assertThat(capturedRecords).hasSize(2);
        assertThat(new String(capturedRecords.get(0).headers().lastHeader("IllumioTenantId").value())).isEqualTo("12345");
        assertThat(new String(capturedRecords.get(1).headers().lastHeader("IllumioTenantId").value())).isEqualTo("12345");
        assertThat(capturedRecords.get(0).value()).isEqualTo("CEF_FLOW_1");
        assertThat(capturedRecords.get(1).value()).isEqualTo("CEF_FLOW_2");

        // Verify the topic is correct
        capturedRecords.forEach(record ->
                assertThat(record.topic()).isEqualTo("syslog-raw-cef-v1"));
    }
}

