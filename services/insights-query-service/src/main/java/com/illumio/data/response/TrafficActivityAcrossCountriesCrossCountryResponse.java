package com.illumio.data.response;

import com.illumio.data.builder.ResponseBuilder;
import com.illumio.data.model.Pagination;
import com.illumio.data.model.RequestContext;
import com.illumio.data.model.RequestPayload;
import com.illumio.data.model.SortByFields;
import com.illumio.data.model.TimeFrame;
import com.illumio.data.model.constants.Metadata;
import com.illumio.data.service.batch_widget.PivotExtractable;
import com.microsoft.azure.kusto.data.KustoResultSetTable;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.illumio.data.model.constants.Fields.ZONE;
import static com.illumio.data.model.constants.Fields.REGION;
import static com.illumio.data.model.constants.Fields.COUNTRY;
import static com.illumio.data.model.constants.Fields.COUNT;
import static com.illumio.data.model.constants.Fields.PREVIOUS_COUNT;
import static com.illumio.data.model.constants.Fields.TRAFFIC_DIRECTION;
import static com.illumio.data.model.constants.Fields.AGGREGATE_FIELD;

@Data
@NoArgsConstructor
@Component
public class TrafficActivityAcrossCountriesCrossCountryResponse implements ResponseBuilder<TrafficActivityAcrossCountriesCrossCountryResponse>, PivotExtractable {
    String widgetId;
    String title;
    TimeFrame currentTimeFrame;
    TimeFrame comparisonTimeFrame;
    List<String> columns;
    List<String> columnTypes;
    List<String> columnDisplayNames;
    List<List<Object>> data;
    List<SortByFields> sortByFields;
    Pagination pagination;

    @Override
    public TrafficActivityAcrossCountriesCrossCountryResponse buildResponse(KustoResultSetTable resultSetTable, RequestContext requestContext, Metadata requestMetadata) {
        RequestPayload payload = requestContext.getRequestPayload().get();
        TrafficActivityAcrossCountriesCrossCountryResponse response = new TrafficActivityAcrossCountriesCrossCountryResponse();

        // Set Widget Specific Metadata
        response.setWidgetId(requestMetadata.getWidgetId());
        response.setTitle("Traffic Activity Across Countries");
        response.setColumns(Arrays.asList(ZONE.getFieldKey(), REGION.getFieldKey(), COUNTRY.getFieldKey(), COUNT.getFieldKey(), PREVIOUS_COUNT.getFieldKey(), TRAFFIC_DIRECTION.getFieldKey(), AGGREGATE_FIELD.getFieldKey()));
        response.setColumnTypes(Arrays.asList(ZONE.getFieldType(), REGION.getFieldType(), COUNTRY.getFieldType(), COUNT.getFieldType(), PREVIOUS_COUNT.getFieldType(), TRAFFIC_DIRECTION.getFieldType(), AGGREGATE_FIELD.getFieldType()));
        response.setColumnDisplayNames(Arrays.asList(ZONE.getFieldDisplayName(), REGION.getFieldDisplayName(), COUNTRY.getFieldDisplayName(), COUNT.getFieldDisplayName(), PREVIOUS_COUNT.getFieldDisplayName(), TRAFFIC_DIRECTION.getFieldDisplayName(), AGGREGATE_FIELD.getFieldDisplayName()));

        // Set Time Frame
        response.setCurrentTimeFrame(payload.getCurrentTimeFrame());
        response.setComparisonTimeFrame(payload.getComparisonTimeFrame());
        response.setPagination(payload.getPagination());
        response.setSortByFields(payload.getSortByFields());

        // Set Response Data
        List<List<Object>> data = new ArrayList<>();
        while (resultSetTable.next()) {
            String zone = (String) resultSetTable.getObject(ZONE.getTableColumnName());
            String region = (String) resultSetTable.getObject(REGION.getTableColumnName());
            String country = (String) resultSetTable.getObject(COUNTRY.getTableColumnName());
            Long count = getLongValue(resultSetTable, COUNT.getTableColumnName());
            Long prevCount = getLongValue(resultSetTable, PREVIOUS_COUNT.getTableColumnName());
            String trafficDirection = (String) resultSetTable.getObject(TRAFFIC_DIRECTION.getTableColumnName());
            String aggregateField = (String) resultSetTable.getObject(AGGREGATE_FIELD.getTableColumnName());

            List<Object> dataRow = new ArrayList<>();
            dataRow.add(zone);
            dataRow.add(region);
            dataRow.add(country);
            dataRow.add(count);
            dataRow.add(prevCount);
            dataRow.add(trafficDirection);
            dataRow.add(aggregateField);

            data.add(dataRow);
        }
        response.setData(data);

        return response;
    }

    @Override
    public TrafficActivityAcrossCountriesCrossCountryResponse buildAggregatedResponse(List<KustoResultSetTable> resultList, Optional<MultiValueMap<String, String>> params) {
        return null;
    }

    private Long getLongValue(KustoResultSetTable resultSetTable, String columnName) {
        Object value = resultSetTable.getObject(columnName);
        return (value != null) ? ((Number) value).longValue() : null;
    }

    /**
     * Converts the response data to CSV format for efficient consumption.
     *
     * @return A Map containing the CSV data and metadata
     */
    public Map<String, Object> compress() {
        Map<String, Object> result = new HashMap<>();
        StringBuilder csvData = new StringBuilder();

        // Add CSV header based on column display names
        csvData.append("Zone,Region,Country,Count,PreviousCount,TrafficDirection,AggregateField\n");

        // Add data rows
        if (data != null && !data.isEmpty()) {
            for (List<Object> row : data) {
                if (row.size() < 7) continue;

                String zone = (String) row.get(0);
                String region = (String) row.get(1);
                String country = (String) row.get(2);
                Long count = (row.get(3) != null) ? ((Number)row.get(3)).longValue() : null;
                Long prevCount = (row.get(4) != null) ? ((Number)row.get(4)).longValue() : null;
                String trafficDirection = (String) row.get(5);
                String aggregateField = (String) row.get(6);

                // Escape fields for CSV format
                String escapedZone = escapeCsvField(zone);
                String escapedRegion = escapeCsvField(region);
                String escapedCountry = escapeCsvField(country);
                String escapedTrafficDirection = escapeCsvField(trafficDirection);
                String escapedAggregateField = escapeCsvField(aggregateField);

                // Add row to CSV
                csvData.append(escapedZone).append(",")
                       .append(escapedRegion).append(",")
                       .append(escapedCountry).append(",")
                       .append(count != null ? count : 0).append(",")
                       .append(prevCount != null ? prevCount : 0).append(",")
                       .append(escapedTrafficDirection).append(",")
                       .append(escapedAggregateField).append("\n");
            }
        }

        // Add metadata
        if (currentTimeFrame != null) {
            result.put("currentTimeFrameStart", currentTimeFrame.getStartTime());
            result.put("currentTimeFrameEnd", currentTimeFrame.getEndTime());
        }

        if (comparisonTimeFrame != null) {
            result.put("comparisonTimeFrameStart", comparisonTimeFrame.getStartTime());
            result.put("comparisonTimeFrameEnd", comparisonTimeFrame.getEndTime());
        }

        if (pagination != null) {
            result.put("pageNumber", pagination.getPageNumber());
            result.put("totalPages", pagination.getTotalPages());
        }

        // Add CSV data and format
        result.put("csvData", csvData.toString());
        result.put("format", "csv");
        result.put("widgetId", widgetId);
        result.put("title", title);

        return result;
    }

    /**
     * Properly escapes a field value for CSV format according to RFC 4180:
     * - If the field contains commas, newlines, or double quotes, it should be enclosed in double quotes
     * - Double quotes within the field should be escaped by doubling them
     *
     * @param value The field value to escape
     * @return The properly escaped CSV field
     */
    private String escapeCsvField(String value) {
        if (value == null) {
            return "";
        }

        boolean needsQuoting = value.contains(",") || value.contains("\"") ||
                               value.contains("\n") || value.contains("\r");

        if (needsQuoting) {
            // Replace any double quotes with two double quotes
            String escapedValue = value.replace("\"", "\"\"");
            return "\"" + escapedValue + "\"";
        } else {
            return value;
        }
    }

}
