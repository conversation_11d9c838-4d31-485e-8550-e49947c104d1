package com.illumio.data.components;

import com.illumio.data.model.CefRecord;
import com.illumio.data.model.TrafficData;
import com.illumio.data.model.TrafficDataRecord;
import com.google.protobuf.ByteString;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.test.StepVerifier;

import java.net.InetAddress;
import java.util.Arrays;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
public class TrafficDataCefMapperTest {

    @Test
    void testCefFromFlow_basicMapping() {
        TrafficDataRecord flow = TrafficDataRecord.newBuilder()
                .setSrcIp(byteStringFromIp("***********"))
                .setDstIp(byteStringFromIp("********"))
                .setDeviceAddr(byteStringFromIp("***************"))
                .setDstPort(443)
                .setProto(6)
                .setCount(11)
                .setTimestamp(1756464198L)
                .setSrcMac(ByteString.copyFromUtf8("aa:bb:cc:dd:ee:ff"))
                .setDstMac(ByteString.copyFromUtf8("ff:ee:dd:cc:bb:aa"))
                .setIcmpType(8)
                .setIcmpCode(0)
                .setSrcMaskBits(32)
                .setDstMaskBits(24)
                .setOctets(6945)
                .build();

        TrafficData trafficData = TrafficData.newBuilder()
                .setOrgId(12345)
                .setPceHost(ByteString.copyFromUtf8("devtest4.ilabs.io:8443"))
                .setInstallationId(ByteString.copyFromUtf8("inst-456"))
                .setAggregationSeconds(60)
                .addAllFlows(Collections.singletonList(flow))
                .build();

        CefRecord cefRecord = TrafficDataCefMapper.cefFromFlow(flow, trafficData).get();
        String cefString = cefRecord.getCefMessage();

        assertEquals("12345", cefRecord.getOrgId());
        assertTrue(cefString.startsWith("CEF:0|Illumio|Flowlink|1.0|100|Traffic Flow|0|"));
        assertTrue(cefString.contains("src=***********"));
        assertTrue(cefString.contains("dst=********"));
        assertTrue(cefString.contains("dvc=***************"));
        assertTrue(cefString.contains("dpt=443"));
        assertTrue(cefString.contains("proto=6"));
        assertTrue(cefString.contains("cnt=11"));
        assertTrue(cefString.contains("start=1756464198000"));
        assertTrue(cefString.contains("end=1756464258000"));
        assertTrue(cefString.contains("smac=aa:bb:cc:dd:ee:ff"));
        assertTrue(cefString.contains("dmac=ff:ee:dd:cc:bb:aa"));
        assertTrue(cefString.contains("smask=32"));
        assertTrue(cefString.contains("dmask=24"));
        assertTrue(cefString.contains("bytes=6945"));
        assertTrue(cefString.contains("rule_action=Accept"));
        assertTrue(cefString.contains("conn_direction=Internal"));
        assertTrue(cefString.contains("cs1Label=OrgId"));
        assertTrue(cefString.contains("cs1=12345"));
        assertTrue(cefString.contains("cs2Label=InstallationId"));
        assertTrue(cefString.contains("cs2=inst-456"));
        assertTrue(cefString.contains("cs3Label=PceHost"));
        assertTrue(cefString.contains("cs3=devtest4.ilabs.io:8443"));
        assertTrue(cefString.contains("cs4Label=AggregationSeconds"));
        assertTrue(cefString.contains("cs4=60"));
        assertTrue(cefString.contains("cs5Label=IcmpType"));
        assertTrue(cefString.contains("cs5=8"));
        assertTrue(cefString.contains("cs6Label=IcmpCode"));
        assertTrue(cefString.contains("cs6=0"));
    }

    @Test
    void testCefFromTrafficData_multipleFlows() {
        TrafficDataRecord flow1 = TrafficDataRecord.newBuilder()
                .setSrcIp(byteStringFromIp("***********"))
                .setDstIp(byteStringFromIp("********"))
                .setDeviceAddr(byteStringFromIp("***************"))
                .setDstPort(443)
                .setProto(6)
                .setCount(11)
                .setTimestamp(1111L)
                .build();

        TrafficDataRecord flow2 = TrafficDataRecord.newBuilder()
                .setSrcIp(byteStringFromIp("***********"))
                .setDstIp(byteStringFromIp("********"))
                .setDeviceAddr(byteStringFromIp("***************"))
                .setDstPort(80)
                .setProto(6)
                .setCount(22)
                .setTimestamp(2222L)
                .build();

        TrafficData trafficData = TrafficData.newBuilder()
                .setOrgId(12345)
                .setPceHost(ByteString.copyFromUtf8("devtest4.ilabs.io"))
                .setInstallationId(ByteString.copyFromUtf8("inst-456"))
                .setAggregationSeconds(60)
                .addAllFlows(Arrays.asList(flow1, flow2))
                .build();

        StepVerifier.create(TrafficDataCefMapper.cefFromTrafficData(trafficData))
                .expectNextMatches(msg -> msg.getCefMessage().contains("src=***********") && msg.getCefMessage().contains("dst=********"))
                .expectNextMatches(msg -> msg.getCefMessage().contains("src=***********") && msg.getCefMessage().contains("dst=********"))
                .verifyComplete();
    }

    @Test
    void testCefFromTrafficData_someValidFlows() {
        TrafficDataRecord flow1 = TrafficDataRecord.newBuilder()
                .setSrcIp(byteStringFromIp("***********"))
                .setDstIp(byteStringFromIp("********"))
                .setDeviceAddr(byteStringFromIp("***************"))
                .setDstPort(443)
                .setProto(6)
                .setCount(11)
                .setTimestamp(1111L)
                .build();

        TrafficDataRecord flow2 = TrafficDataRecord.newBuilder()
                .setSrcIp(ByteString.copyFromUtf8("invalidip"))
                .setDstIp(byteStringFromIp("********"))
                .setDeviceAddr(byteStringFromIp("***************"))
                .setDstPort(80)
                .setProto(6)
                .setCount(22)
                .setTimestamp(2222L)
                .build();

        TrafficData trafficData = TrafficData.newBuilder()
                .setOrgId(12345)
                .setPceHost(ByteString.copyFromUtf8("devtest4.ilabs.io"))
                .setInstallationId(ByteString.copyFromUtf8("inst-456"))
                .setAggregationSeconds(60)
                .addAllFlows(Arrays.asList(flow1, flow2))
                .build();

        StepVerifier.create(TrafficDataCefMapper.cefFromTrafficData(trafficData))
                .expectNextMatches(msg -> msg.getCefMessage().contains("src=***********") && msg.getCefMessage().contains("dst=********"))
                .verifyComplete();
    }

    @Test
    void testCefFromFlow_escapeSpecialCharacters() {
        TrafficDataRecord flow = TrafficDataRecord.newBuilder()
                .setSrcIp(byteStringFromIp("***********"))
                .setDstIp(byteStringFromIp("********"))
                .setDeviceAddr(byteStringFromIp("***************"))
                .setDstPort(443)
                .setProto(6)
                .setCount(1)
                .setTimestamp(12345L)
                .build();

        TrafficData trafficData = TrafficData.newBuilder()
                .setOrgId(12345)
                .setPceHost(ByteString.copyFromUtf8("devtest\\4.ilabs.io"))
                .setInstallationId(ByteString.copyFromUtf8("inst=45|6"))
                .setAggregationSeconds(60)
                .addAllFlows(Collections.singletonList(flow))
                .build();

        CefRecord cefRecord = TrafficDataCefMapper.cefFromFlow(flow, trafficData).get();
        String cefString = cefRecord.getCefMessage();

        assertEquals("12345", cefRecord.getOrgId());
        assertTrue(cefString.contains("src=***********"));
        assertTrue(cefString.contains("cs1=12345"));
        assertTrue(cefString.contains("cs2=inst\\=45\\|6"));
        assertTrue(cefString.contains("cs3=devtest\\\\4.ilabs.io"));
    }

    @Test
    void testCefFromTrafficData_emptyFlows() {
        TrafficData trafficData = TrafficData.newBuilder()
                .setOrgId(12345)
                .setPceHost(ByteString.copyFromUtf8("devtest4.ilabs.io"))
                .setInstallationId(ByteString.copyFromUtf8("inst-456"))
                .setAggregationSeconds(60)
                .addAllFlows(Collections.emptyList())
                .build();

        StepVerifier.create(TrafficDataCefMapper.cefFromTrafficData(trafficData))
                .verifyComplete(); // No emissions expected
    }

    @SneakyThrows
    private ByteString byteStringFromIp(String ip) {
        return ByteString.copyFrom(InetAddress.getByName(ip).getAddress());
    }
}
