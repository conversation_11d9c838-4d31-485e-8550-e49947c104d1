# Default values for label recommendation service.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
logging:
  level:
    root: INFO
    org:
      apache:
        kafka: INFO

ports:
  - name: admin
    port: 8084
  - name: rest
    port: 8081

servicePorts:
  - name: rest
    podPort: rest
    servicePort: 8081

service:
  type: ClusterIP

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

replicaCount: 1

image:
  repositoryBase: illum.azurecr.io/
  repositoryName: label-recommendation
  tag:      # value given at helm deployment
  pullPolicy: Always

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 6
  targetCPUUtilizationPercentage: 60
  targetMemoryUtilizationPercentage: 60

podAnnotations: {}

podMetricsAnnotations:
  prometheus.io/path: /metrics
  prometheus.io/port: "9464"
  prometheus.io/scheme: http
  prometheus.io/scrape: "true"



podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000



resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

nodeSelector: {}

tolerations: []

affinity: {}

extraLabels: {}

ansi:
  enabled: ALWAYS
webApplicationType: none
labelRecommendation:
  kafkaCommonConfig:
    bootstrapServers: eventhubs-connector.servicebus.windows.net:9093
    isSasl: true
    saslJaasConfig: _DO_NOT_COMMIT_
    metadataMaxAgeMs: 180000
    connectionMaxIdleMs: 180000
  kafkaReceiverConfig:
    topic: ip-classification-v1
    groupId: label-recommendation-group
    isGroupInstanceIdEnabled: false # Flag for adding the group.instance.id as pod name in env variable
    autoOffsetReset: latest
    heartbeatIntervalMs: 3000
    requestTimeoutMs: 60000
    sessionTimeoutMs: 60000
    maxPollIntervalMs: 300000
    maxPollRecords: 5000
    maxPartitionFetchBytes: 5242880
    partitionAssignmentStrategy: "org.apache.kafka.clients.consumer.CooperativeStickyAssignor"
  kafkaSenderConfig:
    topic: ml-classification-v1
    maxRequestSize: 1000000
    requestTimeoutMs: 60000
    metadataMaxIdleMs: 180000
    deliveryTimeoutMs: 300000
    lingerMs: 20
    bufferMemoryMb: 48
    batchSizeKb: 64
  dbInitConfig:
    url: ************************************************************************************************
    username: _DO_NOT_COMMIT_
    password: _DO_NOT_COMMIT_
    svcDbName: autolabel_output
  postgresConfig:
    url: r2dbc:pool:postgresql://label-recommendation.postgres.database.azure.com:5432/autolabel_output?sslMode=REQUIRE
    username: _DO_NOT_COMMIT_
    password: _DO_NOT_COMMIT_
    sslMode: REQUIRE
  redisConfig:
    host: label-recommendation.redis.cache.windows.net
    port: 6380
    password: _DO_NOT_COMMIT_
    useSsl: true
    commandTimeoutMs: 120000
  cacheConfig:
    localTtlDuration: PT3H
    maxKeys: 120000

jvmOptions: "-XX:MaxRAMPercentage=70.0 -XX:+CrashOnOutOfMemoryError"

eventhub:
  password:                 # should give at deployment time