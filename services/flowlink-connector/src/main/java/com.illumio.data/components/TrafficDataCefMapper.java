package com.illumio.data.components;

import java.net.InetAddress;
import java.util.*;

import com.illumio.data.model.CefRecord;
import com.illumio.data.model.TrafficData;
import com.illumio.data.model.TrafficDataRecord;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;

@Slf4j
@UtilityClass
public class TrafficDataCefMapper {

    public static Flux<CefRecord> cefFromTrafficData(TrafficData trafficData) {
        return Flux.fromStream(trafficData.getFlowsList().stream()
                .map(trafficDataRecord -> cefFromFlow(trafficDataRecord, trafficData))
                .filter(Optional::isPresent)
                .map(Optional::get));
    }

    public static Optional<CefRecord> cefFromFlow(TrafficDataRecord flow, TrafficData trafficData) {
        Map<String, String> extensions = new LinkedHashMap<>();
        // Standard fields
        try {
            extensions.put("src", InetAddress.getByAddress(flow.getSrcIp().toByteArray()).getHostAddress());
            extensions.put("dst", InetAddress.getByAddress(flow.getDstIp().toByteArray()).getHostAddress());
        } catch (Exception e) {
            log.warn("Ignoring trafficDataRecord={} due to malformed IP", flow);
            return Optional.empty();
        }
        try {
            extensions.put("dvc", InetAddress.getByAddress(flow.getDeviceAddr().toByteArray()).getHostAddress());
        } catch (Exception e) {
            log.warn("Flow trafficDataRecord={} from trafficData={} has a malformed device address", flow, trafficData);
        }
        extensions.put("dpt", String.valueOf(flow.getDstPort()));
        extensions.put("proto", String.valueOf(flow.getProto()));
        extensions.put("cnt", String.valueOf(flow.getCount()));
        extensions.put("smac", String.valueOf(flow.getSrcMac().toStringUtf8()));
        extensions.put("dmac", String.valueOf(flow.getDstMac().toStringUtf8()));
        extensions.put("smask", String.valueOf(flow.getSrcMaskBits()));
        extensions.put("dmask", String.valueOf(flow.getDstMaskBits()));
        extensions.put("bytes", String.valueOf(flow.getOctets()));
        extensions.put("rule_action", "Accept");
        extensions.put("conn_direction", "Internal"); // TODO: remove once we can differentiate in/out bytes
        extensions.put("rt", String.valueOf(System.currentTimeMillis()));
        long startTimeEpochMillis = flow.getTimestamp() * 1000;
        long endTimeEpochMillis = startTimeEpochMillis + trafficData.getAggregationSeconds() * 1000L;
        extensions.put("start", String.valueOf(startTimeEpochMillis));
        extensions.put("end", String.valueOf(endTimeEpochMillis));

        // Custom fields
        extensions.put("cs1Label", "OrgId");
        final String orgId = String.valueOf(trafficData.getOrgId());
        if (orgId.length() < 2) {
            log.warn("Ignoring trafficDataRecord={} due to invalid org ID", flow);
            return Optional.empty();
        }
        extensions.put("cs1", orgId);

        extensions.put("cs2Label", "InstallationId");
        extensions.put("cs2", trafficData.getInstallationId().toStringUtf8());

        extensions.put("cs3Label", "PceHost");
        extensions.put("cs3", trafficData.getPceHost().toStringUtf8());

        extensions.put("cs4Label", "AggregationSeconds");
        extensions.put("cs4", String.valueOf(trafficData.getAggregationSeconds()));

        extensions.put("cs5Label", "IcmpType");
        extensions.put("cs5", String.valueOf(flow.getIcmpType()));

        extensions.put("cs6Label", "IcmpCode");
        extensions.put("cs6", String.valueOf(flow.getIcmpCode()));

        final String cefMessage = formatFlowlinkCefString(extensions);
        return Optional.of(
                CefRecord.builder()
                        .orgId(orgId)
                        .cefMessage(cefMessage)
                        .build());
    }

    private static String formatFlowlinkCefString(Map<String, String> extensions) {
        final String cefVersion = "0";
        final String deviceVendor = "Illumio";
        final String deviceProduct = "Flowlink";
        final String deviceVersion = "1.0";
        final String deviceEventClassId = "100";
        final String name = "Traffic Flow";
        final String severity = "0";

        StringBuilder sb = new StringBuilder();
        sb.append("CEF:")
                .append(cefVersion).append("|")
                .append(deviceVendor).append("|")
                .append(deviceProduct).append("|")
                .append(deviceVersion).append("|")
                .append(deviceEventClassId).append("|")
                .append(name).append("|")
                .append(severity).append("|");

        if (!extensions.isEmpty()) {
            StringJoiner joiner = new StringJoiner(" ");
            for (Map.Entry<String, String> entry : extensions.entrySet()) {
                joiner.add(entry.getKey() + "=" + escape(entry.getValue()));
            }
            sb.append(joiner);
        }

        return sb.toString();
    }

    /**
     * Escape characters according to CEF spec
     */
    private static String escape(String value) {
        if (value == null) return "";
        return value.replace("\\", "\\\\")
                .replace("|", "\\|")
                .replace("=", "\\=");
    }

}
