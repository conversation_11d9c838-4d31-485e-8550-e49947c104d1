package com.illumio.data.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.reactive.CorsWebFilter;
import org.springframework.web.cors.reactive.UrlBasedCorsConfigurationSource;

import java.util.Arrays;

@Configuration
public class GlobalCorsConfig {

    @Bean
    public CorsWebFilter corsWebFilter() {
        CorsConfiguration corsConfig = new CorsConfiguration();

        // Allow specific origins
        corsConfig.setAllowedOrigins(Arrays.asList(
                "https://graph.illum.io",
                "https://insights.redmond.ilabs.io",
                "https://insights.sunnyvale.ilabs.io",
                "https://console.sunnyvale.ilabs.io",
                "https://console.redmond.ilabs.io",
                "https://dev.console.ilabs.io",
                "https://stage.console.ilabs.io",
                "https://prod.console.ilabs.io",
                "https://gw.console.illum.io",
                "https://console.illum.io",
                "https://stage-gw.console.ilabs.io",
                "https://dev-gw.console.ilabs.io",
                "http://localhost:8081",
                "https://localhost:8443",
                "https://localhost:8479"
        ));
        corsConfig.setAllowedOriginPatterns(Arrays.asList(
                "https://*.console.ilabs.io",
                "https://*.illumiograph.io",
                "https://*.core.ilabs.io"
        ));

        // Allow all methods
        corsConfig.setAllowedMethods(Arrays.asList("*"));

        // Allow all headers
        corsConfig.setAllowedHeaders(Arrays.asList("*"));

        // Allow credentials
        corsConfig.setAllowCredentials(true);

        // Set max age
        corsConfig.setMaxAge(3600L);

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", corsConfig);

        return new CorsWebFilter(source);
    }
}
