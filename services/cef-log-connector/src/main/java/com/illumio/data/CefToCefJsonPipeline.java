package com.illumio.data;

import com.azure.messaging.eventhubs.EventData;
import com.azure.messaging.eventhubs.models.PartitionContext;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.jcustenborder.cef.Message;
import com.illumio.data.components.UtilityFunctions;

import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.qpid.proton.amqp.Binary;
import org.springframework.stereotype.Component;

import reactor.core.publisher.Mono;
import reactor.kafka.sender.SenderRecord;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

@Component
@Slf4j
@RequiredArgsConstructor
public class CefToCefJsonPipeline {
    private final ObjectMapper objectMapper;
    private static final String ILLUMIO_TENANT_ID = "IllumioTenantId";
    private static final String CEF_DATA = "cef-data";

    public Mono<SenderRecord<String, String, String>> pipeline(PartitionContext partitionContext, EventData eventData, Mono<Message> cefMessageMono, String kafkaTopic) {
        return cefMessageMono
                .flatMap(message -> decorateCefMessageWithTenantId(message, eventData.getProperties()))
                .flatMap(message -> createSenderRecord(message, partitionContext, eventData,kafkaTopic))
                .doOnNext(senderRecord -> log.debug("Created Cef Message record: {}", senderRecord));
    }

    private Mono<SenderRecord<String, String, String>> createSenderRecord(
            Message cefMessage, PartitionContext partitionContext, EventData eventData, String kafkaTopic) {

        ProducerRecord<String, String> producerRecord =
                new ProducerRecord<>(
                        kafkaTopic,
                        null,
                        createCefMessageJson(cefMessage));
        producerRecord.headers().add("data-type", createHeaderValue().getBytes());
        return Mono.just(
                SenderRecord.create(
                        producerRecord,
                        UtilityFunctions.eventDataString(partitionContext, eventData)));

    }

    @SneakyThrows
    private String createCefMessageJson(Message cefMessage)  {
        Map<String, Object> serializableMap = convertToSerializableMap(cefMessage);
        return objectMapper.writeValueAsString(serializableMap);
    }

    private Map<String, Object> convertToSerializableMap(Message message) {
        Map<String, Object> result = new HashMap<>();

        result.put("version", message.cefVersion());
        result.put("deviceVendor", message.deviceVendor());
        result.put("deviceProduct", message.deviceProduct());
        result.put("deviceVersion", message.deviceVersion());
        result.put("deviceEventClassId", message.deviceEventClassId());
        result.put("name", message.name());
        result.put("severity", message.severity());

        Map<String, Object> extensions = new HashMap<>();
        if (message.extensions() != null) {
            message.extensions().forEach((key, value) -> {
                if (value != null) {
                    extensions.put(key, value.toString());
                }
            });
        }
        result.put("extensions", extensions);

        return result;
    }

    private Mono<Message> decorateCefMessageWithTenantId(Message cefMessage, Map<String, Object> properties) {
        cefMessage.extensions().put("tenantId", getTenantId(properties));
        return Mono.just(cefMessage);
    }

    private String getTenantId(Map<String, Object> properties) {
        if (properties.containsKey(ILLUMIO_TENANT_ID)) {
            Binary binary = (Binary) properties.get(ILLUMIO_TENANT_ID);
            return convertBinaryToString(binary);
        }
        return "";
    }

    private String convertBinaryToString(Binary binary) {
        if (binary == null) {
            return null;
        }
        byte[] bytes = binary.getArray();
        return new String(bytes, binary.getArrayOffset(), binary.getLength(), StandardCharsets.UTF_8);
    }

    private String createHeaderValue() {
        return CEF_DATA;
    }


}