package com.illumio.data.util;

import io.opentelemetry.api.common.Attributes;
import io.opentelemetry.api.metrics.LongCounter;
import io.opentelemetry.api.metrics.ObservableLongGauge;
import io.opentelemetry.api.metrics.Meter;
import org.springframework.stereotype.Component;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;

@Component
public class MetricsUtil {
    private final LongCounter insightIPClassificationEventCounter;
    private final LongCounter insightIPClassificationWrongEvent;
    private final LongCounter ipClassificationOutgoingEvent;
    private final LongCounter consumerRecreationEvent;
    private final LongCounter senderRecreationEvent;
    private final LongCounter senderRecreationFailureEvent;

    // For the gauge, we need to store the current value and attributes
    private final AtomicLong assignedPartitionsCount = new AtomicLong(0);
    private final AtomicReference<Attributes> assignedPartitionsAttributes = new AtomicReference<>(Attributes.empty());

    private LongCounter createCounter(Meter meter, String name, String description) {
        return meter.counterBuilder(name)
            .setDescription(description)
            .build();
    }

    private ObservableLongGauge createGauge(Meter meter, String name, String description) {
        return meter.gaugeBuilder(name)
            .setDescription(description)
            .ofLongs()
            .buildWithCallback(measurement -> {
                measurement.record(assignedPartitionsCount.get(), assignedPartitionsAttributes.get());
            });
    }

    public MetricsUtil(Meter meter) {

        // Use existing beans
        this.insightIPClassificationEventCounter = this.createCounter(meter, FlowDataValidator.INSIGHTS_INCOMING_EVENTS,
            "Used to count the number of IP Classification events");
        this.insightIPClassificationWrongEvent = this.createCounter(meter, FlowDataValidator.INSIGHTS_INCOMING_WRONG_EVENTS,
            "Used to count the number of malformed events that reached IP Classification");
        this.ipClassificationOutgoingEvent = this.createCounter(meter, FlowDataValidator.INSIGHTS_OUTGOING_EVENTS,
            "Used to count the number of IP Classification outgoing events");
        this.consumerRecreationEvent = this.createCounter(meter, "consumer_recreation_events",
            "Used to count the number of consumer recreation events in IP Classification service");
        this.senderRecreationEvent = this.createCounter(meter, "sender_recreation_events",
            "Used to count the number of sender recreation events in IP Classification service");
        this.senderRecreationFailureEvent = this.createCounter(meter, "sender_recreation_failure_events",
            "Used to count the number of failed sender recreation events in IP Classification service");

        // Create the observable gauge - it will automatically call the callback to get current values
        this.createGauge(meter, "client_consumed_partitions",
            "Number of Kafka partitions currently assigned to this consumer");
    }

    public void incrementIPClassificationEvent(Attributes attributes) {
        insightIPClassificationEventCounter.add(1L, attributes);
    }

    public void incrementIPClassificationWrongEvent(Attributes attributes) {
        insightIPClassificationWrongEvent.add(1L, attributes);
    }

    public void incrementIPClassificationOutgoingEvent(Attributes attributes) {
        ipClassificationOutgoingEvent.add(1L, attributes);
    }

    public void incrementConsumerRecreationEvent(Attributes attributes) {
        consumerRecreationEvent.add(1L, attributes);
    }

    public void incrementSenderRecreationEvent(Attributes attributes) {
        senderRecreationEvent.add(1L, attributes);
    }

    public void incrementSenderRecreationFailureEvent(Attributes attributes) {
        senderRecreationFailureEvent.add(1L, attributes);
    }

    public void setAssignedPartitionsCount(long count, Attributes attributes) {
        assignedPartitionsCount.set(count);
        assignedPartitionsAttributes.set(attributes);
    }
}
