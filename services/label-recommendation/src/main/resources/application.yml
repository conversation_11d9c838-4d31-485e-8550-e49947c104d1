logging:
  level:
    ROOT: DEBUG
    org:
      apache:
        kafka: INFO
spring:
  application:
    name: label-recommendation
  main:
    web-application-type: none
  output:
    ansi:
      enabled: ALWAYS

label-recommendation:
  kafka-common-config:
    bootstrap-servers: eventhubs-connector.servicebus.windows.net:9093
    is-sasl: true
    sasl-jaas-config: _DO_NOT_COMMIT_
    metadata-max-age-ms: 180000
    connection-max-idle-ms: 180000
  kafka-receiver-config:
    topic: ip-classification-v1
    group-id: label-recommendation-group
    auto-offset-reset: latest
    heartbeat-interval-ms: 3000
    request-timeout-ms: 60000
    session-timeout-ms: 60000
    max-poll-interval-ms: 300000
    max-poll-records: 5000
    max-partition-fetch-bytes: 5242880
    partition-assignment-strategy: org.apache.kafka.clients.consumer.CooperativeStickyAssignor
  kafka-sender-config:
    topic: ml-classification-v1
    max-request-size: 1000000
    request-timeout-ms: 60000
    metadata-max-idle-ms: 180000
    delivery-timeout-ms: 300000
    linger-ms: 10
    buffer-memory-mb: 48
    batch-size-kb: 32
  db-init-config:
    url: ************************************************************************************************
    username: _DO_NOT_COMMIT_
    password: _DO_NOT_COMMIT_
    database: autolabel_output
  datasource-config:
    url: r2dbc:pool:postgresql://label-recommendation.postgres.database.azure.com:5432/autolabel_output?sslMode=REQUIRE
    username: _DO_NOT_COMMIT_
    password: _DO_NOT_COMMIT_
    ssl-mode: REQUIRE
  redis-config:
    host: label-recommendation.redis.cache.windows.net
    port: 6380
    password: _DO_NOT_COMMIT_
    use-ssl: true
    command-timeout-ms: 120000
  cache-config:
    local-ttl-duration: PT3H
    max-keys: 120000