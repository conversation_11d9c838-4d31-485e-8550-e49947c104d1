package com.illumio.data.utils;

import com.illumio.data.model.Filters;
import com.illumio.data.model.constants.TrafficDirection;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.illumio.data.model.constants.Fields.TRAFFIC_DIRECTION;

public class ApplicationUtils {
    public static TrafficDirection getTrafficDirection(List<Filters> filters) {
        return Optional.ofNullable(filters)
                .flatMap(fs -> fs.stream()
                        .filter(f -> TRAFFIC_DIRECTION.getFieldKey().equalsIgnoreCase(f.getCategoryName()))
                        .map(Filters::getCategoryValue)
                        .flatMap(list -> list.stream().map(Object::toString))
                        .findFirst()
                )
                .map(TrafficDirection::fromString)
                .orElse(TrafficDirection.BOTH);
    }

    public static void updateTrafficDirectionForBidirectionalQueries(List<Filters> filters,TrafficDirection trafficDirection) {
        if (trafficDirection != TrafficDirection.INBOUND && trafficDirection != TrafficDirection.OUTBOUND) {
            throw new IllegalArgumentException("Direction must be either 'INBOUND' or 'OUTBOUND'");
        }
        Optional<Filters> optionalFilter = filters.stream()
                .filter(f -> TRAFFIC_DIRECTION.getFieldKey().equalsIgnoreCase(f.getCategoryName()))
                .findFirst();

        if (optionalFilter.isPresent()) {
            optionalFilter.get().setCategoryValue(Collections.singletonList(trafficDirection.toString().toUpperCase()));
        } else {
            filters.add(Filters
                    .builder()
                    .categoryName(TRAFFIC_DIRECTION.getFieldKey())
                    .categoryValue(Collections.singletonList(trafficDirection.toString().toUpperCase()))
                    .categoryType("string")
                    .build());
        }
    }

    public static void clearTrafficDirection(List<Filters> filters) {
        Optional<Filters> trafficDirectionFilter = filters.stream()
                .filter(f -> TRAFFIC_DIRECTION.getFieldKey().equalsIgnoreCase(f.getCategoryName()))
                .findFirst();

        trafficDirectionFilter.ifPresent(filters::remove);
    }
}
