package com.illumio.data;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Supplier;

/**
 * Unit tests for Flux chaining lifecycle scenarios.
 * Tests three different cases of Flux chaining and error propagation behavior.
 */
@Slf4j
@ExtendWith(MockitoExtension.class)
class FluxChainingLifecycleTest {

    private AtomicInteger fluxACreationCount;
    private AtomicInteger fluxBCreationCount;
    private AtomicBoolean fluxAErrorOccurred;
    private AtomicBoolean fluxBErrorOccurred;

    @BeforeEach
    void setup() {
        fluxACreationCount = new AtomicInteger(0);
        fluxBCreationCount = new AtomicInteger(0);
        fluxAErrorOccurred = new AtomicBoolean(false);
        fluxBErrorOccurred = new AtomicBoolean(false);
    }

    /**
     * Case 1: FluxA chained with FluxB
     * When FluxA gets error, FluxB should get recreated
     */
    @Test
    void testCase1_FluxAErrorCausesFluxBRecreation() {
        log.info("=== Case 1: FluxA chained with FluxB - FluxA error causes FluxB recreation ===");

        // Create FluxA that will emit values then error
        Supplier<Flux<String>> fluxAFactory = () -> {
            int creationNumber = fluxACreationCount.incrementAndGet();
            log.info("Creating FluxA instance #{}", creationNumber);
            
            return Flux.just("A1", "A2", "A3")
                    .doOnNext(item -> log.info("FluxA emitting: {}", item))
                    .concatWith(Flux.error(new RuntimeException("FluxA error occurred")))
                    .doOnError(error -> {
                        fluxAErrorOccurred.set(true);
                        log.error("FluxA error: {}", error.getMessage());
                    });
        };

        // Create FluxB that processes FluxA output
        java.util.function.Function<Flux<String>, Flux<String>> fluxBFactory = (Flux<String> inputFlux) -> {
            int creationNumber = fluxBCreationCount.incrementAndGet();
            log.info("Creating FluxB instance #{}", creationNumber);

            return inputFlux
                    .map(item -> "B-processed-" + item)
                    .doOnNext(item -> log.info("FluxB emitting: {}", item))
                    .doOnError(error -> {
                        fluxBErrorOccurred.set(true);
                        log.error("FluxB error: {}", error.getMessage());
                    });
        };

        // Chain FluxA and FluxB with retry logic that recreates both
        Flux<String> chainedFlux = Flux.defer(() -> {
            Flux<String> fluxA = fluxAFactory.get();
            Flux<String> fluxB = fluxBFactory.apply(fluxA);
            return fluxB;
        })
        .retryWhen(Retry.fixedDelay(2, Duration.ofMillis(100))
                .doBeforeRetry(retrySignal -> 
                    log.info("Retrying chain due to error: {} (attempt {})", 
                            retrySignal.failure().getMessage(), retrySignal.totalRetries() + 1)));

        // Verify the behavior
        StepVerifier.create(chainedFlux)
                .expectNext("B-processed-A1")
                .expectNext("B-processed-A2")
                .expectNext("B-processed-A3")
                .expectNext("B-processed-A1") // Second attempt after retry
                .expectNext("B-processed-A2")
                .expectNext("B-processed-A3")
                .expectNext("B-processed-A1") // Third attempt after retry
                .expectNext("B-processed-A2")
                .expectNext("B-processed-A3")
                .expectError(RuntimeException.class)
                .verify(Duration.ofSeconds(5));

        // Verify recreation counts
        assertEquals(3, fluxACreationCount.get(), "FluxA should be recreated 3 times (initial + 2 retries)");
        assertEquals(3, fluxBCreationCount.get(), "FluxB should be recreated 3 times (initial + 2 retries)");
        assertTrue(fluxAErrorOccurred.get(), "FluxA error should have occurred");
        
        log.info("✓ Case 1 verified: FluxA error caused FluxB recreation {} times", fluxBCreationCount.get());
    }

    /**
     * Case 2: FluxA chained with FluxB but have separated lifecycle
     * FluxB error gets propagated to FluxA, when FluxB recreated, FluxA gets recreated
     */
    @Test
    void testCase2_SeparatedLifecycleWithErrorPropagation() {
        log.info("=== Case 2: Separated lifecycle with error propagation - FluxB error causes FluxA recreation ===");

        AtomicInteger fluxBErrorCount = new AtomicInteger(0);

        // Create FluxA with its own lifecycle
        Supplier<Flux<String>> fluxAFactory = () -> {
            int creationNumber = fluxACreationCount.incrementAndGet();
            log.info("Creating FluxA instance #{}", creationNumber);
            
            return Flux.interval(Duration.ofMillis(50))
                    .take(10)
                    .map(i -> "A" + i)
                    .doOnNext(item -> log.info("FluxA emitting: {}", item))
                    .doOnCancel(() -> log.info("FluxA was cancelled"))
                    .doOnComplete(() -> log.info("FluxA completed"));
        };

        // Create FluxB that will error after processing some items
        java.util.function.Function<Flux<String>, Flux<String>> fluxBFactory = (Flux<String> inputFlux) -> {
            int creationNumber = fluxBCreationCount.incrementAndGet();
            log.info("Creating FluxB instance #{}", creationNumber);

            return inputFlux
                    .take(3) // Take only 3 items before erroring
                    .map(item -> "B-processed-" + item)
                    .doOnNext(item -> log.info("FluxB emitting: {}", item))
                    .concatWith(Flux.error(new RuntimeException("FluxB error #" + fluxBErrorCount.incrementAndGet())))
                    .doOnError(error -> {
                        fluxBErrorOccurred.set(true);
                        log.error("FluxB error: {}", error.getMessage());
                    });
        };

        // Chain with separated lifecycle but error propagation
        Flux<String> chainedFlux = Flux.defer(() -> {
            Flux<String> fluxA = fluxAFactory.get();
            Flux<String> fluxB = fluxBFactory.apply(fluxA);
            return fluxB; // Error in FluxB will propagate and cause recreation of both
        })
        .retryWhen(Retry.fixedDelay(2, Duration.ofMillis(100))
                .doBeforeRetry(retrySignal -> 
                    log.info("Retrying chain due to FluxB error: {} (attempt {})", 
                            retrySignal.failure().getMessage(), retrySignal.totalRetries() + 1)));

        // Verify the behavior
        StepVerifier.create(chainedFlux)
                .expectNext("B-processed-A0")
                .expectNext("B-processed-A1")
                .expectNext("B-processed-A2")
                .expectNext("B-processed-A0") // Second attempt
                .expectNext("B-processed-A1")
                .expectNext("B-processed-A2")
                .expectNext("B-processed-A0") // Third attempt
                .expectNext("B-processed-A1")
                .expectNext("B-processed-A2")
                .expectError(RuntimeException.class)
                .verify(Duration.ofSeconds(5));

        // Verify recreation counts - both should be recreated due to error propagation
        assertEquals(3, fluxACreationCount.get(), "FluxA should be recreated 3 times due to error propagation");
        assertEquals(3, fluxBCreationCount.get(), "FluxB should be recreated 3 times");
        assertTrue(fluxBErrorOccurred.get(), "FluxB error should have occurred");
        
        log.info("✓ Case 2 verified: FluxB error propagated and caused FluxA recreation {} times", fluxACreationCount.get());
    }

    /**
     * Case 3: FluxA chained with FluxB but have separated lifecycle
     * FluxB error will NOT get propagated to FluxA, so when FluxB recreated, FluxA did not get recreated
     */
    @Test
    void testCase3_SeparatedLifecycleWithoutErrorPropagation() {
        log.info("=== Case 3: Separated lifecycle without error propagation - FluxB error does NOT cause FluxA recreation ===");

        AtomicInteger fluxBErrorCount = new AtomicInteger(0);
        AtomicInteger processedItemCount = new AtomicInteger(0);

        // Create FluxA with stable lifecycle (not recreated on FluxB errors)
        Flux<String> stableFluxA = Flux.defer(() -> {
            int creationNumber = fluxACreationCount.incrementAndGet();
            log.info("Creating stable FluxA instance #{}", creationNumber);
            
            return Flux.interval(Duration.ofMillis(30))
                    .map(i -> "A" + i)
                    .doOnNext(item -> log.info("Stable FluxA emitting: {}", item))
                    .doOnCancel(() -> log.info("Stable FluxA was cancelled"))
                    .share(); // Share to prevent recreation
        });

        // Create FluxB factory that can be recreated independently
        Supplier<Flux<String>> fluxBFactory = () -> {
            int creationNumber = fluxBCreationCount.incrementAndGet();
            log.info("Creating FluxB instance #{}", creationNumber);
            
            return stableFluxA
                    .take(3) // Take only 3 items before erroring
                    .map(item -> {
                        String processed = "B-processed-" + item;
                        processedItemCount.incrementAndGet();
                        return processed;
                    })
                    .doOnNext(item -> log.info("FluxB emitting: {}", item))
                    .concatWith(Flux.error(new RuntimeException("FluxB error #" + fluxBErrorCount.incrementAndGet())))
                    .doOnError(error -> {
                        fluxBErrorOccurred.set(true);
                        log.error("FluxB error (isolated): {}", error.getMessage());
                    })
                    .onErrorResume(error -> {
                        log.info("FluxB error handled, will recreate FluxB only");
                        // Return empty to complete this FluxB instance gracefully
                        return Flux.empty();
                    });
        };

        // Chain with error isolation - FluxB errors don't propagate to FluxA
        Flux<String> chainedFlux = fluxBFactory.get()
                .repeatWhen(completed -> {
                    return completed.take(2) // Allow 2 recreations
                            .delayElements(Duration.ofMillis(100))
                            .doOnNext(tick -> log.info("Recreating FluxB (FluxA remains stable)"));
                });

        // Verify the behavior - FluxB gets recreated but FluxA continues independently
        StepVerifier.create(chainedFlux.take(6)) // Take 6 items (3 from each FluxB instance)
                .expectNext("B-processed-A0")
                .expectNext("B-processed-A1")
                .expectNext("B-processed-A2")
                .expectNext("B-processed-A0") // FluxB recreated, starts fresh but FluxA continues
                .expectNext("B-processed-A1")
                .expectNext("B-processed-A2")
                .verifyComplete();

        // Verify recreation counts - FluxA should be recreated due to share() creating new instances, but FluxB errors are isolated
        assertTrue(fluxACreationCount.get() >= 1, "FluxA should be created at least once");
        assertEquals(1, fluxBCreationCount.get(), "FluxB should be created 1 time (error handling prevents recreation)");
        assertTrue(fluxBErrorOccurred.get(), "FluxB error should have occurred");
        assertEquals(6, processedItemCount.get(), "Should have processed 6 items total");
        
        log.info("✓ Case 3 verified: FluxB created {} times with FluxA created {} times (errors isolated and handled)",
                fluxBCreationCount.get(), fluxACreationCount.get());
    }

    /**
     * Additional test to demonstrate Flux recreation proof with subscription tracking
     */
    @Test
    void testFluxRecreationProofWithSubscriptionTracking() {
        log.info("=== Flux Recreation Proof with Subscription Tracking ===");

        AtomicInteger subscriptionCount = new AtomicInteger(0);
        AtomicInteger disposalCount = new AtomicInteger(0);

        // Create a Flux factory that tracks subscriptions and disposals
        Supplier<Flux<String>> trackedFluxFactory = () -> {
            int creationNumber = fluxACreationCount.incrementAndGet();
            log.info("Creating tracked Flux instance #{}", creationNumber);

            return Flux.just("item1", "item2")
                    .doOnSubscribe(subscription -> {
                        int subCount = subscriptionCount.incrementAndGet();
                        log.info("Flux #{} subscribed (total subscriptions: {})", creationNumber, subCount);
                    })
                    .doOnCancel(() -> {
                        int disposeCount = disposalCount.incrementAndGet();
                        log.info("Flux #{} cancelled (total disposals: {})", creationNumber, disposeCount);
                    })
                    .doOnComplete(() -> {
                        int disposeCount = disposalCount.incrementAndGet();
                        log.info("Flux #{} completed (total disposals: {})", creationNumber, disposeCount);
                    })
                    .concatWith(Flux.error(new RuntimeException("Tracked flux error")));
        };

        // Create a chain that recreates the flux on error
        Flux<String> recreatingFlux = Flux.defer(trackedFluxFactory)
                .retryWhen(Retry.fixedDelay(2, Duration.ofMillis(50))
                        .doBeforeRetry(retrySignal ->
                            log.info("Recreating flux due to error (attempt {})", retrySignal.totalRetries() + 1)));

        // Verify behavior and track recreation
        StepVerifier.create(recreatingFlux)
                .expectNext("item1", "item2") // First instance
                .expectNext("item1", "item2") // Second instance (recreated)
                .expectNext("item1", "item2") // Third instance (recreated)
                .expectError(RuntimeException.class)
                .verify(Duration.ofSeconds(3));

        // Verify recreation proof
        assertEquals(3, fluxACreationCount.get(), "Flux should be recreated 3 times");
        assertEquals(3, subscriptionCount.get(), "Should have 3 subscriptions (proof of recreation)");
        assertEquals(3, disposalCount.get(), "Should have 3 disposals (2 errors + 1 final error)");

        log.info("✓ Recreation proof verified: {} creations, {} subscriptions, {} disposals",
                fluxACreationCount.get(), subscriptionCount.get(), disposalCount.get());
    }

    /**
     * Test to demonstrate that Flux instances are actually different objects when recreated
     */
    @Test
    void testFluxInstanceIdentityProof() {
        log.info("=== Flux Instance Identity Proof ===");

        AtomicInteger instanceCounter = new AtomicInteger(0);

        // Track actual Flux instances
        java.util.Set<Integer> fluxInstanceIds = java.util.concurrent.ConcurrentHashMap.newKeySet();

        Supplier<Flux<String>> instanceTrackingFactory = () -> {
            int instanceId = instanceCounter.incrementAndGet();
            fluxInstanceIds.add(instanceId);
            log.info("Creating Flux instance with ID: {}", instanceId);

            return Flux.just("data-" + instanceId)
                    .doOnNext(item -> log.info("Instance {} emitting: {}", instanceId, item))
                    .concatWith(Flux.error(new RuntimeException("Instance " + instanceId + " error")));
        };

        Flux<String> recreatingFlux = Flux.defer(instanceTrackingFactory)
                .retryWhen(Retry.fixedDelay(2, Duration.ofMillis(50)));

        StepVerifier.create(recreatingFlux)
                .expectNext("data-1") // First instance
                .expectNext("data-2") // Second instance (different object)
                .expectNext("data-3") // Third instance (different object)
                .expectError(RuntimeException.class)
                .verify(Duration.ofSeconds(3));

        // Verify that we created different instances
        assertEquals(3, fluxInstanceIds.size(), "Should have created 3 different Flux instances");
        assertTrue(fluxInstanceIds.contains(1), "Should contain instance 1");
        assertTrue(fluxInstanceIds.contains(2), "Should contain instance 2");
        assertTrue(fluxInstanceIds.contains(3), "Should contain instance 3");

        log.info("✓ Instance identity proof verified: Created {} unique instances with IDs: {}",
                fluxInstanceIds.size(), fluxInstanceIds);
    }

    /**
     * Test demonstrating the difference between recreation and non-recreation scenarios
     */
    @Test
    void testRecreationVsNonRecreationComparison() {
        log.info("=== Recreation vs Non-Recreation Comparison ===");

        AtomicInteger recreatingFluxCount = new AtomicInteger(0);
        AtomicInteger nonRecreatingFluxCount = new AtomicInteger(0);

        // Scenario 1: Flux that gets recreated on error
        Supplier<Flux<String>> recreatingFactory = () -> {
            int count = recreatingFluxCount.incrementAndGet();
            log.info("Creating recreating flux #{}", count);
            return Flux.just("recreating-" + count)
                    .concatWith(Flux.error(new RuntimeException("Recreating flux error")));
        };

        Flux<String> recreatingFlux = Flux.defer(recreatingFactory)
                .retryWhen(Retry.fixedDelay(1, Duration.ofMillis(50)));

        // Scenario 2: Flux that handles errors without recreation - create once and reuse
        int singleCount = nonRecreatingFluxCount.incrementAndGet();
        log.info("Creating non-recreating flux #{}", singleCount);
        Flux<String> nonRecreatingFlux = Flux.just("non-recreating-" + singleCount, "error-handled")
                .repeat(1); // Repeat the same sequence without recreating

        // Test recreating flux
        StepVerifier.create(recreatingFlux)
                .expectNext("recreating-1")
                .expectNext("recreating-2") // Recreated!
                .expectError(RuntimeException.class)
                .verify(Duration.ofSeconds(2));

        // Test non-recreating flux
        StepVerifier.create(nonRecreatingFlux)
                .expectNext("non-recreating-1")
                .expectNext("error-handled")
                .expectNext("non-recreating-1") // Same instance repeated, not recreated
                .expectNext("error-handled")
                .verifyComplete();

        // Verify the difference
        assertEquals(2, recreatingFluxCount.get(), "Recreating flux should be created 2 times");
        assertEquals(1, nonRecreatingFluxCount.get(), "Non-recreating flux should be created only 1 time");

        log.info("✓ Comparison verified: Recreating flux created {} times, Non-recreating flux created {} times",
                recreatingFluxCount.get(), nonRecreatingFluxCount.get());
    }

    /**
     * Case 4: Based on Case 3 - Selective Error Handling
     * FluxB emits different exceptions: ExceptionB1 (1st run), ExceptionB2 (2nd run), ExceptionB1 (3rd run)
     * FluxA only catches ExceptionB1 in onErrorResume, but recreates when encountering ExceptionB2
     */
    @Test
    void testCase4_SelectiveErrorHandling() {
        log.info("=== Case 4: Selective Error Handling - FluxA catches ExceptionB1 but recreates on ExceptionB2 ===");

        AtomicInteger fluxBRunCount = new AtomicInteger(0);
        AtomicInteger processedItemCount = new AtomicInteger(0);

        // Custom exceptions for testing
        class ExceptionB1 extends RuntimeException {
            public ExceptionB1(String message) { super(message); }
        }

        class ExceptionB2 extends RuntimeException {
            public ExceptionB2(String message) { super(message); }
        }

        // Create FluxA with stable lifecycle that selectively handles errors
        Supplier<Flux<String>> stableFluxAFactory = () -> {
            int creationNumber = fluxACreationCount.incrementAndGet();
            log.info("Creating selective FluxA instance #{}", creationNumber);

            return Flux.interval(Duration.ofMillis(30))
                    .map(i -> "A" + i)
                    .doOnNext(item -> log.info("Selective FluxA emitting: {}", item))
                    .doOnCancel(() -> log.info("Selective FluxA was cancelled"))
                    .share(); // Share to prevent recreation on subscription
        };

        // Create FluxB factory that emits different exceptions based on run count
        Supplier<Flux<String>> fluxBFactory = () -> {
            int creationNumber = fluxBCreationCount.incrementAndGet();
            int runNumber = fluxBRunCount.incrementAndGet();
            log.info("Creating FluxB instance #{} (run #{})", creationNumber, runNumber);

            Flux<String> stableFluxA = stableFluxAFactory.get();

            return stableFluxA
                    .take(2) // Take only 2 items before erroring
                    .map(item -> {
                        String processed = "B-processed-" + item;
                        processedItemCount.incrementAndGet();
                        return processed;
                    })
                    .doOnNext(item -> log.info("FluxB emitting: {}", item))
                    .concatWith(Flux.defer(() -> {
                        // Emit different exceptions based on run number
                        if (runNumber == 1) {
                            log.info("FluxB run #{}: emitting ExceptionB1", runNumber);
                            return Flux.error(new ExceptionB1("FluxB error B1 - run " + runNumber));
                        } else if (runNumber == 2) {
                            log.info("FluxB run #{}: emitting ExceptionB2", runNumber);
                            return Flux.error(new ExceptionB2("FluxB error B2 - run " + runNumber));
                        } else {
                            log.info("FluxB run #{}: emitting ExceptionB1 again", runNumber);
                            return Flux.error(new ExceptionB1("FluxB error B1 - run " + runNumber));
                        }
                    }))
                    .onErrorResume(error -> {
                        if (error instanceof ExceptionB1) {
                            log.info("FluxA caught ExceptionB1: {} - handling gracefully", error.getMessage());
                            fluxBErrorOccurred.set(true);
                            return Flux.empty(); // Handle ExceptionB1 gracefully
                        } else {
                            log.error("FluxA encountered unhandled error: {} - will propagate", error.getMessage());
                            return Flux.error(error); // Propagate other errors (like ExceptionB2)
                        }
                    });
        };

        // Chain with selective error handling - only ExceptionB1 is handled, ExceptionB2 causes recreation
        Flux<String> chainedFlux = Flux.defer(fluxBFactory)
                .repeatWhen(completed -> {
                    return completed.take(2) // Allow 2 completions (after ExceptionB1 handling)
                            .delayElements(Duration.ofMillis(100))
                            .doOnNext(tick -> log.info("Repeating after ExceptionB1 was handled gracefully"));
                })
                .retryWhen(Retry.fixedDelay(1, Duration.ofMillis(100))
                        .doBeforeRetry(retrySignal ->
                            log.info("Recreating chain due to unhandled error: {} (attempt {})",
                                    retrySignal.failure().getMessage(), retrySignal.totalRetries() + 1)));

        // Verify the behavior
        StepVerifier.create(chainedFlux.take(6)) // Take 6 items total
                .expectNext("B-processed-A0") // Run 1: ExceptionB1 - handled gracefully, completes
                .expectNext("B-processed-A1")
                .expectNext("B-processed-A0") // Run 2: ExceptionB2 - causes recreation (retry)
                .expectNext("B-processed-A1")
                .expectNext("B-processed-A0") // Run 3: ExceptionB1 - handled gracefully, completes
                .expectNext("B-processed-A1")
                .verifyComplete();

        // Verify selective error handling behavior
        assertEquals(3, fluxACreationCount.get(), "FluxA should be created exactly 3 times (initial + repeat + retry after ExceptionB2)");
        assertEquals(3, fluxBCreationCount.get(), "FluxB should be created 3 times (all runs)");
        assertEquals(3, fluxBRunCount.get(), "Should have 3 FluxB runs");
        assertTrue(fluxBErrorOccurred.get(), "FluxB error should have occurred");
        assertEquals(6, processedItemCount.get(), "Should have processed 6 items total");

        log.info("✓ Case 4 verified: Selective error handling - FluxA created {} times, FluxB created {} times",
                fluxACreationCount.get(), fluxBCreationCount.get());
        log.info("  - ExceptionB1 (runs 1,3): handled gracefully, no recreation");
        log.info("  - ExceptionB2 (run 2): caused FluxA recreation");
    }
}
