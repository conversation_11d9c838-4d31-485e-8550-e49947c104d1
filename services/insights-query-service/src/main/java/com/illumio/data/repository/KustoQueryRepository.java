package com.illumio.data.repository;
import com.illumio.data.model.constants.Metadata;
import com.illumio.data.model.constants.TrafficDirection;
import com.illumio.data.model.constants.WidgetId;
import com.illumio.data.utils.ApplicationUtils;
import lombok.extern.slf4j.Slf4j;
import com.illumio.data.model.DerivedMetadata;
import com.illumio.data.model.Filters;
import com.illumio.data.model.Pagination;
import com.illumio.data.model.RequestContext;
import com.illumio.data.model.RequestPayload;
import com.illumio.data.model.SortByFields;
import com.illumio.data.model.TimeCondition;
import com.illumio.data.model.TimeFrame;
import com.illumio.data.model.constants.Fields;
import com.illumio.data.repository.timequery.TimeConditionStrategyFactory;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.illumio.data.model.constants.Fields.AGGREGATED_BYTES;
import static com.illumio.data.model.constants.Fields.AGGREGATED_FLOWS;
import static com.illumio.data.model.constants.Fields.AGGREGATE_FIELD;
import static com.illumio.data.model.constants.Fields.CATEGORY_ID;
import static com.illumio.data.model.constants.Fields.COUNT;
import static com.illumio.data.model.constants.Fields.COUNTRY;
import static com.illumio.data.model.constants.Fields.DESTINATION_EXTERNAL_LABEL;
import static com.illumio.data.model.constants.Fields.DESTINATION_RESOURCE_ID;
import static com.illumio.data.model.constants.Fields.DESTINATION_ROLE;
import static com.illumio.data.model.constants.Fields.PORT;
import static com.illumio.data.model.constants.Fields.PROTOCOL;
import static com.illumio.data.model.constants.Fields.ROLE;
import static com.illumio.data.model.constants.Fields.SOURCE_REGION;
import static com.illumio.data.model.constants.Fields.SOURCE_RESOURCE_CATEGORY;
import static com.illumio.data.model.constants.Fields.SOURCE_RESOURCE_ID;
import static com.illumio.data.model.constants.Fields.SOURCE_RESOURCE_NAME;
import static com.illumio.data.model.constants.Fields.SOURCE_RESOURCE_TYPE;
import static com.illumio.data.model.constants.Fields.TOTAL_ROWS;
import static com.illumio.data.model.constants.Fields.SOURCE_ROLE;
import static com.illumio.data.model.constants.Fields.DESTINATION_COUNTRY;
import static com.illumio.data.model.constants.Fields.SOURCE_IP;
import static com.illumio.data.model.constants.Fields.PREVIOUS_COUNT;
import static com.illumio.data.model.constants.Fields.START_TIME;
import static com.illumio.data.model.constants.Fields.TRAFFIC_DIRECTION;

@Slf4j
@Repository
@AllArgsConstructor
public class KustoQueryRepository {

    private static final String BASE_TABLE = "DecoratedFlows ";
    private static final String TENANT_ID = "| where IllumioTenantId == '%s'";
    private static final String UNION_QUERY = "union %s, %s;";
    private static final String EXTEND_AGGREGATE_FIELD = "| extend %s = '%s' ";
    private static final String QUERY_DATA_LIMIT = "| take %s";
    private static final Integer TOP_QUERIES_LIMIT = 15;

    public String getQueryString(RequestContext requestContext, Metadata metadata) {

        // Generate time condition dynamically based on table name and timeframe including the default time condition to avoid timeframe overlaps and honor month & weekly bucket timeframe
        DerivedMetadata derivedMetadata = requestContext.getDerivedMetadata().get();
        String currentTimeCondition = TimeConditionStrategyFactory
                                        .getStrategy(derivedMetadata.getCurrentTableType())
                                        .buildTimeConditionQuery(derivedMetadata.getUpdatedCurrentTimeFrame().getStartTime(),
                                                            derivedMetadata.getUpdatedCurrentTimeFrame().getEndTime(),
                                                            derivedMetadata.getCurrentTimeFrameTable());
        String comparisonTimeCondition = TimeConditionStrategyFactory
                                            .getStrategy(derivedMetadata.getComparisonTableType())
                                            .buildTimeConditionQuery(derivedMetadata.getUpdatedComparisonTimeFrame().getStartTime(),
                                                    derivedMetadata.getUpdatedComparisonTimeFrame().getEndTime(),
                                                    derivedMetadata.getComparedTimeFrameTable());

        TimeCondition timeCondition = TimeCondition.builder()
                                        .currentTimeCondition(currentTimeCondition)
                                        .comparisonTimeCondition(comparisonTimeCondition)
                                        .build();

        return switch (metadata.getWidgetId()) {
            case WidgetId.RISKY_SERVICE_TRAFFIC, WidgetId.RISKY_SERVICE_TRAFFIC_INSIGHTS_HUB -> getRiskyServiceTrafficQuery(requestContext, timeCondition, metadata);
            case WidgetId.ZONE_LEVEL_TRAFFIC -> getZoneLevelTrafficQuery(requestContext, timeCondition, metadata);
            case WidgetId.DESTINATION_ROLE_LEVEL_TRAFFIC -> getDestRoleTrafficQuery(requestContext, timeCondition, metadata);
            case WidgetId.TOP_DESTINATION_ROLES, WidgetId.TOP_DESTINATION_ROLES_INSIGHTS_HUB -> getTopDestinationRoles(requestContext, timeCondition, metadata);
            case WidgetId.TOP_WORKLOADS -> getTopWorkloads(requestContext, timeCondition, metadata);
            case WidgetId.TOP_MALICIOUS_IPS, WidgetId.TOP_MALICIOUS_IPS_INSIGHTS_HUB -> getTopMaliciousIps(requestContext, timeCondition, metadata);
            case WidgetId.THREAT_MAP -> getThreatMap(requestContext, timeCondition, metadata);
            case WidgetId.TOP_CATEGORY_WITH_MALICIOUS_IP -> getTopCategoryWithMaliciousIPs(requestContext, timeCondition, metadata);
            case WidgetId.TOP_ROLES -> getTopRoles(requestContext, timeCondition, metadata);
            case WidgetId.TOP_SERVICES -> getTopServices(requestContext, timeCondition, metadata);
            case WidgetId.EXTERNAL_DESTINATION_CATEGORY -> getExternalCategoryTransferQuery(requestContext, timeCondition, metadata);
            case WidgetId.TOP_SOURCE_ROLE_TRANSFER -> getTopSourceRoleTransferQuery(requestContext, timeCondition, metadata);
            case WidgetId.EXTERNAL_GEO_TRANSFER -> getExternalGeoTransferQuery(requestContext, timeCondition, metadata);
            case WidgetId.TOP_SOURCE_TRANSFER, WidgetId.TOP_SOURCE_TRANSFER_INSIGHTS_HUB -> getTopSourceTransferQuery(requestContext, timeCondition, metadata);
            case WidgetId.EXTERNAL_SERVICE_TRANSFER -> getExternalTransferByServiceQuery(requestContext, timeCondition, metadata);
            case WidgetId.LLM_IN_USE -> getLLMInUse(requestContext, timeCondition, metadata);
            case WidgetId.TOP_CATEGORY_WITH_LLM -> getTopCategoryWithLlm(requestContext, timeCondition, metadata);
            case WidgetId.TOP_SOURCES_WITH_LLM -> getTopSourcesWithLlm(requestContext, timeCondition, metadata);
            case WidgetId.THIRD_PARTY_DEPENDENCY_INBOUND -> getThirdPartyDependencyInbound(requestContext, timeCondition, metadata);
            case WidgetId.THIRD_PARTY_DEPENDENCY_OUTBOUND -> getThirdPartyDependencyOutbound(requestContext, timeCondition, metadata);
            case WidgetId.TOP_CROSS_REGION_TRAFFIC -> getCrossRegionTrafficQuery(requestContext, timeCondition, metadata);
            case WidgetId.TOP_REGION_TO_COUNTRY_TRAFFIC -> getRegionToCountryTrafficQuery(requestContext, timeCondition, metadata);
            case WidgetId.RISKY_TRAFFIC_BY_ROLES_RESOURCE_INSIGHTS -> getRiskyTrafficByRolesResourceInsightsQuery(requestContext, timeCondition);
            case WidgetId.MALICIOUS_IP_TRAFFIC_RESOURCE_INSIGHTS -> getMaliciousIpTrafficResourceInsightsQuery(requestContext, timeCondition, metadata);
            case WidgetId.EXTERNAL_DATA_TRANSFER_RESOURCE_INSIGHTS -> getExternalDataTransfredResourceInsightsQuery(requestContext, timeCondition);
            case WidgetId.UNENCRYPTED_SERVICES -> getUnencryptedServices(requestContext, timeCondition, metadata);
            case WidgetId.DORA_TOP_ICT -> getDoraTopIct(requestContext, timeCondition);
            case WidgetId.DORA_CRITICAL_ICT -> getCriticalIctQuery(requestContext, timeCondition);
            case WidgetId.RISKY_SERVICES_TRAFFIC_RESOURCE_INSIGHTS -> getRiskyServiceTrafficResourceInsightsQuery(requestContext, timeCondition, metadata);
            case WidgetId.IP_TOP_DESTINATION_RESOURCES -> getTopDestinationResources(requestContext, timeCondition, metadata);
            case WidgetId.IP_TOP_SOURCE_RESOURCES -> getTopSourceResources(requestContext, timeCondition, metadata);
            case WidgetId.IP_TOP_DESTINATION_ROLES -> getTopDestinationRoles(requestContext, timeCondition, metadata);
            case WidgetId.IP_TOP_SOURCE_ROLES -> getTopSourceRoles(requestContext, timeCondition, metadata);
            case WidgetId.IP_DATA_TRANSFER_INBOUND -> getIpDataTransferInbound(requestContext, timeCondition, metadata);
            case WidgetId.IP_DATA_TRANSFER_OUTBOUND -> getIpDataTransferOutbound(requestContext, timeCondition, metadata);
            case WidgetId.GET_CSP_REGIONS -> getCspRegions(requestContext, timeCondition, metadata);
            case WidgetId.TRAFFIC_ACTIVITY_ACROSS_COUNTRIES_CROSS_COUNTRY -> getTrafficActivityAcrossCountriesCrossCountryQuery(requestContext, timeCondition, metadata);
            case WidgetId.TOP_CROSS_REGION_TRAFFIC_COUNTRY_INSIGHTS -> getCrossRegionTrafficCountryInsightsQuery(requestContext, timeCondition, metadata);
            case WidgetId.GET_RISKY_SERVICE_NAMES -> getRiskyServiceNames(requestContext, timeCondition, metadata);
            case WidgetId.RISKY_TRAFFIC_TOP_SUBSCRIPTIONS -> getRiskyTrafficTopSubscriptionsWithCountry(requestContext, timeCondition, metadata);
            default -> throw new IllegalArgumentException("Unsupported WidgetId: " + metadata.getWidgetId());
        };
    }


    public String getMetadataQuery(String widgetId) {
        return  "RequestMetadata| where widgetId == '"+widgetId+"'| top 1 by ingestion_time() desc ;";
    }

    public String getRiskyResourceQueryString(String xAzureTenantId, String xIllumioTenantId, String startTime) {
        String timeFilter = " ";
        if(startTime != null) {
            timeFilter = "| where ingestion_time() > unixtime_milliseconds_todatetime(" + startTime + ") ";
        }
        // Default start time to 1 day ago if not mentioned
        else {
            timeFilter = " | where ingestion_time() > ago(1d) ";
        }
        return "let portProtoCombinations = datatable(Port:int, Protocol:string) " +
                "[ " + "    1, \"TCP\", " + "    7, \"TCP\", " + "    7, \"UDP\", " + "    9, \"TCP\", " + "    9, \"UDP\", " + "    11, \"TCP\", " + "    13, \"TCP\", " + "    13, \"UDP\", " + "    17, \"TCP\", " + "    17, \"UDP\", " + "    18, \"TCP\", " + "    19, \"TCP\", " + "    19, \"UDP\", " + "    20, \"TCP\", " + "    21, \"TCP\", " + "    22, \"TCP\", " + "    22, \"UDP\", " + "    23, \"TCP\", " + "    23, \"UDP\", " + "    25, \"TCP\", " + "    37, \"TCP\", " + "    37, \"UDP\", " + "    43, \"TCP\", " + "    49, \"TCP\", " + "    49, \"UDP\", " + "    53, \"TCP\", " + "    53, \"TCP\", " + "    69, \"UDP\", " + "    70, \"TCP\", " + "    71, \"TCP\", " + "    71, \"UDP\", " + "    72, \"TCP\", " + "    72, \"UDP\", " + "    73, \"TCP\", " + "    73, \"UDP\", " + "    74, \"TCP\", " + "    74, \"UDP\", " + "    79, \"TCP\", " + "    80, \"TCP\", " + "    81, \"TCP\", " + "    82, \"TCP\", " + "    83, \"TCP\", " + "    84, \"TCP\", " + "    88, \"TCP\", " + "    88, \"UDP\", " + "    95, \"TCP\", " + "    101, \"TCP\", " + "    102, \"TCP\", " + "    104, \"TCP\", " + "    105, \"TCP\", " + "    107, \"TCP\", " + "    108, \"TCP\", " + "    109, \"TCP\", " + "    110, \"TCP\", " + "    111, \"TCP\", " + "    111, \"UDP\", " + "    113, \"TCP\", " + "    115, \"TCP\", " + "    117, \"TCP\", " + "    118, \"TCP\", " + "    119, \"TCP\", " + "    126, \"TCP\", " + "    135, \"TCP\", " + "    137, \"UDP\", " + "    138, \"UDP\", " + "    139, \"UDP\", " + "    143, \"TCP\", " + "    152, \"TCP\", " + "    153, \"TCP\", " + "    153, \"UDP\", " + "    156, \"TCP\", " + "    158, \"TCP\", " + "    161, \"TCP\", " + "    162, \"TCP\", " + "    162, \"UDP\", " + "    170, \"TCP\", " + "    177, \"UDP\", " + "    179, \"TCP\", " + "    194, \"TCP\", " + "    199, \"TCP\", " + "    201, \"TCP\", " + "    209, \"TCP\", " + "    210, \"TCP\", " + "    213, \"TCP\", " + "    218, \"TCP\", " + "    220, \"TCP\", " + "    259, \"TCP\", " + "    443, \"TCP\", " + "    445, \"TCP\", " + "    445, \"UDP\", " + "    512, \"TCP\", " + "    514, \"TCP\", " + "    593, \"TCP\", " + "    593, \"UDP\", " + "    873, \"TCP\", " + "    1080, \"TCP\", " + "    1080, \"UDP\", " + "    1081, \"TCP\", " + "    1082, \"TCP\", " + "    1083, \"TCP\", " + "    1083, \"UDP\", " + "    1443, \"TCP\", " + "    1723, \"TCP\", " + "    1723, \"UDP\", " + "    1900, \"UDP\", " + "    2020, \"TCP\", " + "    2020, \"UDP\", " + "    2049, \"TCP\", " + "    2049, \"UDP\", " + "    2080, \"TCP\", " + "    2081, \"TCP\", " + "    2081, \"UDP\", " + "    2082, \"TCP\", " + "    2082, \"UDP\", " + "    2083, \"TCP\", " + "    3080, \"TCP\", " + "    3080, \"UDP\", " + "    3081, \"TCP\", " + "    3082, \"TCP\", " + "    3083, \"TCP\", " + "    3128, \"TCP\", " + "    3389, \"TCP\", " + "    3389, \"UDP\", " + "    3702, \"TCP\", " + "    3702, \"UDP\", " + "    4004, \"TCP\", " + "    4040, \"TCP\", " + "    4040, \"UDP\", " + "    4080, \"TCP\", " + "    4080, \"UDP\", " + "    4100, \"TCP\", " + "    4100, \"UDP\", " + "    4444, \"TCP\", " + "    4444, \"UDP\", " + "    4444, \"TCP\", " + "    4444, \"UDP\", " + "    5080, \"TCP\", " + "    5080, \"UDP\", " + "    5081, \"TCP\", " + "    5081, \"UDP\", " + "    5082, \"UDP\", " + "    5082, \"UDP\", " + "    5083, \"TCP\", " + "    5083, \"UDP\", " + "    5353, \"UDP\", " + "    5355, \"UDP\", " + "    5800, \"TCP\", " + "    5800, \"UDP\", " + "    5900, \"UDP\", " + "    5900, \"TCP\", " + "    5938, \"TCP\", " + "    5938, \"UDP\", " + "    5985, \"TCP\", " + "    5986, \"TCP\", " + "    6080, \"TCP\", " + "    6081, \"UDP\", " + "    6082, \"TCP\", " + "    6082, \"UDP\", " + "    6083, \"TCP\", " + "    6090, \"TCP\", " + "    6090, \"UDP\", " + "    7000, \"TCP\", " + "    7001, \"TCP\", " + "    7080, \"TCP\", " + "    7081, \"TCP\", " + "    7081, \"UDP\", " + "    7082, \"TCP\", " + "    7083, \" \", " + "    8001, \"TCP\", " + "    8001, \"UDP\", " + "    8008, \"TCP\", " + "    8009, \"TCP\", " + "    8009, \"UDP\", " + "    8020, \"TCP\", " + "    8080, \"TCP\", " + "    8081, \"TCP\", " + "    8082, \"TCP\", " + "    8083, \"TCP\", " + "    8083, \"UDP\", " + "    8083, \"TCP\", " + "    8083, \"UDP\", " + "    8088, \"TCP\", " + "    8089, \"TCP\", " + "    8090, \"TCP\", " + "    8181, \"TCP\", " + "    8181, \"UDP\", " + "    8448, \"TCP\", " + "    8448, \"UDP\", " + "    8800, \"TCP\", " + "    8887, \"TCP\", " + "    8887, \"UDP\", " + "    8888, \"TCP\", " + "    8889, \"TCP\", " + "    8889, \"UDP\", " + "    9000, \"TCP\", " + "    9080, \"TCP\", " + "    9080, \"UDP\", " + "    9083, \"TCP\", " + "    9083, \"UDP\", " + "    9090, \"TCP\", " + "    9229, \"TCP\", " + "    9981, \"TCP\", " + "    10000, \"TCP\", " + "    10080, \"TCP\", " + "    10081, \"TCP\", " + "    10082, \"TCP\", " + "    10082, \"UDP\", " + "    11211, \"UDP\", " + "    21114, \"TCP\", " + "    21114, \"UDP\", " + "    21115, \"TCP\", " + "    21115, \"UDP\", " + "    21116, \"TCP\", " + "    21116, \"UDP\", " + "    21117, \"TCP\", " + "    21117, \"UDP\", " + "    21118, \"TCP\", " + "    21118, \"UDP\", " + "    21119, \"UDP\", " + "    21119, \"TCP\", " + "]; " +
                BASE_TABLE +
                timeFilter +
                "| where IllumioTenantId == '" + xIllumioTenantId + "'  " +
//                "| where DestTenantId == '" + xAzureTenantId + "'  " +
                "| where toscalar(portProtoCombinations  " +
                "        | project Combine = strcat(Port, \",\", Protocol)  " +
                "        | summarize PortsProtocols = make_set(Combine)) " +
                "        contains strcat(Port, \",\", Proto) " +
                "| where isnotempty(DestResId) " +
                "| where isnotempty(Port) " +
                "| where isnotempty(Proto) " +
                "| summarize TotalSentBytes = sum(SentBytes), " + "TotalReceivedBytes = sum(ReceivedBytes), " + "FlowCount = sum(FlowCount), " + "IngestionTime = max(ingestion_time()) " +
                "by ResourceId = DestResId, " + "ResourceInternalId = CSDestId, " + "IllumioTenantId, " + "ResourceTenantId = DestTenantId, " + "ResourceSubId = DestSubId, " + "ResourceRegion = DestRegion, " + "ResourceVnetId = DestVnetId, " + "Port, " + "Proto, " + "TrafficStatus, " + "SourceLabel, " + "DestinationLabel ";
    }

    public String getMaliciousResourceQueryString(String xAzureTenantId, String xIllumioTenantId, String startTime) {
        String timeFilter = " ";
        if(startTime != null) {
            timeFilter = "| where ingestion_time() > unixtime_milliseconds_todatetime(" + startTime + ") ";
        }
        // Default start time to 1 day ago if not mentioned
        else {
            timeFilter = " | where ingestion_time() > ago(1d) ";
        }
        return "let outboundIPs =  " + BASE_TABLE +
                timeFilter +
                "| where IllumioTenantId == '" + xIllumioTenantId + "'  " +
//                "| where DestTenantId == '" + xAzureTenantId + "'  " +
                "| where DestThreatLevel >= 2 " +
                "| where isnotempty(SrcResId) " +
                "| summarize " +
                "TotalBytes = sum(SentBytes) + sum(ReceivedBytes), " + "TotalSentBytes = sum(SentBytes), " + "TotalReceivedBytes = sum(ReceivedBytes), " + "FlowCount = sum(FlowCount), " + "IngestionTime = max(ingestion_time()) " +
                "by " +
                "ResourceId = SrcResId, " + "ResourceInternalId = CSDestId, " + "IllumioTenantId, " + "ResourceTenantId = DestTenantId, " + "ResourceSubId = SrcSubId, " + "ResourceRegion = SrcRegion, " + "ResourceVnetId = SrcVnetId, " + "DestIP, " + "DestDomain, " + "DestThreatLevel, " + "DestIsWellknown, " + "DestCity, " + "DestCountry, " + "SourceLabel, " + "DestinationLabel; " +
                "let inboundIPs =  " + BASE_TABLE +
                timeFilter +
                "| where IllumioTenantId == '" + xIllumioTenantId + "'  " +
//                "| where SrcTenantId == '" + xAzureTenantId + "'  " +
                "| where SrcThreatLevel >= 2 " +
                "| where isnotempty( DestResId)  " +
                "| summarize  " +
                "TotalBytes = sum(SentBytes) + sum(ReceivedBytes), " + "TotalSentBytes = sum(SentBytes), " + "TotalReceivedBytes = sum(ReceivedBytes), " + "FlowCount = sum(FlowCount), " + "IngestionTime = max(ingestion_time()) " +
                "by " +
                "ResourceId = DestResId,  " +
                "ResourceInternalId = CSDestId, " + "IllumioTenantId, " + "ResourceTenantId = DestTenantId, " + "ResourceSubId = DestSubId, " + "ResourceRegion = DestRegion, " + "ResourceVnetId = DestVnetId, " + "SrcIP,  " +
                "SrcDomain, " + "SrcThreatLevel, " + "SrcIsWellknown, " + "SrcCity, " + "SrcCountry, " + "SourceLabel, " + "DestinationLabel; " +
                "inboundIPs " +
                "| union outboundIPs";
    }

    public String getMLLabelResourceQueryString(String xAzureTenantId, String xIllumioTenantId, String startTime) {
        String timeFilter = " ";
        if(startTime != null) {
            timeFilter = "| where ingestion_time() > unixtime_milliseconds_todatetime(" + startTime + ") ";
        }
        // Default start time to 1 day ago if not mentioned
        else {
            timeFilter = " | where ingestion_time() > ago(1d) ";
        }
        return  "let srcResourceLabels = " + BASE_TABLE +
                timeFilter +
                "| where IllumioTenantId == '" + xIllumioTenantId + "'  " +
//                "| where DestTenantId == '" + xAzureTenantId + "'  " +
                "| where isnotempty(SrcResId) " +
                "| where isnotempty(SourceLabel)  " +
                "| summarize " +
                "TotalBytes = sum(SentBytes) + sum(ReceivedBytes), " + "TotalSentBytes = sum(SentBytes), " + "TotalReceivedBytes = sum(ReceivedBytes), " + "FlowCount = sum(FlowCount), " + "IngestionTime = max(ingestion_time()) " +
                "by " +
                "ResourceId = SrcResId, " + "ResourceInternalId = CSSrcId, " + "IllumioTenantId, " + "ResourceTenantId = SrcTenantId, " + "ResourceSubId = SrcSubId, " + "ResourceRegion = SrcRegion, " + "ResourceVnetId = SrcVnetId, " + "SourceLabel; " +

                "let destResourceLabels = " + BASE_TABLE +
                timeFilter +
                "| where IllumioTenantId == '" + xIllumioTenantId + "'  " +
//                "| where DestTenantId == '" + xAzureTenantId + "'  " +
                "| where isnotempty(DestResId) " +
                "| where isnotempty(DestinationLabel)  " +
                "| summarize " +
                "TotalBytes = sum(SentBytes) + sum(ReceivedBytes), " + "TotalSentBytes = sum(SentBytes), " + "TotalReceivedBytes = sum(ReceivedBytes), " + "FlowCount = sum(FlowCount), " + "IngestionTime = max(ingestion_time()) " +
                "by " +
                "ResourceId = DestResId, " + "ResourceInternalId = CSDestId, " + "IllumioTenantId, " + "ResourceTenantId = DestTenantId, " + "ResourceSubId = DestSubId, " + "ResourceRegion = DestRegion, " + "ResourceVnetId = DestVnetId, " + "DestinationLabel; " +

                "union srcResourceLabels, destResourceLabels ;";
    }

    public String getLLMResourceQueryString(String xAzureTenantId, String xIllumioTenantId, String startTime) {
        String timeFilter = " ";
        if(startTime != null) {
            timeFilter = "| where ingestion_time() > unixtime_milliseconds_todatetime(" + startTime + ") ";
        }
        // Default start time to 1 day ago if not mentioned
        else {
            timeFilter = " | where ingestion_time() > ago(1d) ";
        }
        return  BASE_TABLE +
                timeFilter +
                "| where IllumioTenantId == '" + xIllumioTenantId + "'  " +
//                "| where SrcTenantId == '" + xAzureTenantId + "'  " +
                "| where DestinationExternalLabelCategory == 'LLM'" +
                "| summarize TotalSentBytes = sum(SentBytes), TotalReceivedBytes = sum(ReceivedBytes), FlowCount = sum(FlowCount), IngestionTime = max(ingestion_time()) by " +
                " ResourceId = SrcResId, ResourceInternalId = CSSrcId, IllumioTenantId, ResourceTenantId = SrcTenantId, ResourceSubId = SrcSubId, ResourceRegion = SrcRegion," +
                " ResourceVnetId = SrcVnetId, LLMName = DestinationExternalLabel, DestDomain, DestCity, DestRegion = DestGeoRegion ;";
    }

    public String getExternalTransferResourceQueryString(String xAzureTenantId, String xIllumioTenantId, String startTime) {
        String timeFilter = " ";
        if(startTime != null) {
            timeFilter = "| where ingestion_time() > unixtime_milliseconds_todatetime(" + startTime + ") ";
        }
        // Default start time to 1 day ago if not mentioned
        else {
            timeFilter = " | where ingestion_time() > ago(1d) ";
        }
        return   BASE_TABLE +
                timeFilter +
                "| where IllumioTenantId == '" + xIllumioTenantId + "'  " +
//                "| where DestTenantId == '" + xAzureTenantId + "'  " +
                " | where isnotempty(SrcResId) and isempty(DestResId) " +
                " | extend octet1 = toint(split(DestIP, \".\")[0]), octet2 = toint(split(DestIP, \".\")[1]) " +
                " | where not ( octet1 == 10 or (octet1 == 192 and octet2 == 168) or (octet1 == 172 and octet2 between (16 .. 31))) " +
                " | summarize TotalSentBytes = sum(SentBytes), TotalReceivedBytes = sum(ReceivedBytes), FlowCount = sum(FlowCount), IngestionTime = max(ingestion_time()) by " +
                " ResourceId = SrcResId, ResourceInternalId = CSSrcId, IllumioTenantId, ResourceTenantId = SrcTenantId, ResourceSubId = SrcSubId, ResourceRegion = SrcRegion, " +
                "ResourceVnetId = SrcVnetId, DestCountry, DestRegion, DestinationExternalLabel, DestinationExternalLabelCategory;";
    }

    // Most queries are compared with a comparison timeframe. This function returns the common part of the query depending on the timeframe
    private String getBaseQueryForTimeFrame(RequestPayload payload, Metadata metadata, String tenantId, String timeCondition, String label, String tableName) {
        return "let " + label + " = " + tableName +
                String.format(TENANT_ID, tenantId) +
                timeCondition +
                transformFiltersToString(payload.getFilters()) +
                transformGroupingToString(metadata.getSummarizeFields(), metadata.getGroupByFields(), Optional.empty()) + " ; ";
    }

    // overloaded method for 'getBaseQueryForTimeFrame' to support additional filters in base query
    private String getBaseQueryForTimeFrame(RequestPayload payload, Metadata metadata, String tenantId, String timeCondition, String label, String tableName, Map<String, String> optionalColumnFilters) {
        String optionalWhereClause = "";
        if (optionalColumnFilters != null && !optionalColumnFilters.isEmpty()) {
            optionalWhereClause = " | where " + optionalColumnFilters.entrySet().stream()
                    .map(entry -> entry.getKey() + " == \"" + entry.getValue() + "\"")
                    .collect(Collectors.joining(" and "));
        }

        return "let " + label + " = " + tableName +
                String.format(TENANT_ID, tenantId) +
                timeCondition +
                transformFiltersToString(payload.getFilters()) +
                optionalWhereClause +
                transformGroupingToString(metadata.getSummarizeFields(), metadata.getGroupByFields(), Optional.empty()) + " ; ";
    }

    // Return Base Queries
    private String getBaseQuery(RequestPayload payload, String tenantId, String tableName, String timeCondition) {
        // using current timeframe table because condition on time is based upon current time frame
        return tableName +
                String.format(TENANT_ID, tenantId) +
                timeCondition +
                transformFiltersToString(payload.getFilters());
    }

    // Return Directional Base Queries based on TrafficDirection
    private String getDirectionalBaseQuery(RequestPayload payload, String tenantId, String tableName, String timeCondition) {
        // using current timeframe table because condition on time is based upon current time frame
        return tableName +
                String.format(TENANT_ID, tenantId) +
                timeCondition +
                transformDirectionalFiltersToString(payload.getFilters());
    }

    // Return Directional Base Queries based on TrafficDirection
    // Provide means to override the existing traffic direction for running direction agnostic queries
    private String getDirectionalBaseQuery(RequestPayload payload, String tenantId, String tableName, String timeCondition, TrafficDirection direction) {
        ApplicationUtils.updateTrafficDirectionForBidirectionalQueries(payload.getFilters(), direction);
        String query = tableName +
                String.format(TENANT_ID, tenantId) +
                timeCondition +
                transformDirectionalFiltersToString(payload.getFilters());
        ApplicationUtils.clearTrafficDirection(payload.getFilters());
        return query;
    }

    /*
    1. Queries for Risky Traffic Page
    */

    public static String generateJoinQuery(String resultName, String leftTable, String rightTable, String joinKey, String aggregateFieldValue) {
        return String.format(
                "let %s = %s | join kind=leftouter(%s) on %s | extend AggregateField=\"%s\" | sort by Count ;",
                resultName,
                leftTable,
                rightTable,
                joinKey,
                aggregateFieldValue
        );
    }

    // risky traffic widget: Join current timeframe with previous -> sort the data -> paginate and return
    private String getRiskyServiceTrafficQuery(RequestContext requestContext, TimeCondition timeCondition, Metadata metadata) {
        RequestPayload payload = requestContext.getRequestPayload().get();
        String tenantId = requestContext.getTenantId().get();
        DerivedMetadata derivedMetadata= requestContext.getDerivedMetadata().get();

        // setting the pagination row limit to -1 to disable pagination for this widget.
        payload.getPagination().setRowLimit(-1);

        return getBaseQueryForTimeFrame(payload, metadata, tenantId, timeCondition.getCurrentTimeCondition(), "curr", derivedMetadata.getCurrentTimeFrameTable()) +
                getBaseQueryForTimeFrame(payload, metadata, tenantId, timeCondition.getComparisonTimeCondition(), "prev", derivedMetadata.getComparedTimeFrameTable()) +
                "let rows = " +
                " curr | join kind = leftouter(prev) on " + getGroupByFields(metadata.getGroupByFields()) +
                transformSortingToString(payload.getSortByFields()) +
                tranformPaginationtoString(payload.getPagination());
    }
    // zone level traffic widget: Join current timeframe with previous -> sort the data -> paginate and return
    private String getZoneLevelTrafficQuery(RequestContext requestContext, TimeCondition timeCondition, Metadata metadata) {
        RequestPayload payload = requestContext.getRequestPayload().get();
        String tenantId = requestContext.getTenantId().get();
        DerivedMetadata derivedMetadata= requestContext.getDerivedMetadata().get();

        return getBaseQueryForTimeFrame(payload, metadata, tenantId, timeCondition.getCurrentTimeCondition(), "curr", derivedMetadata.getCurrentTimeFrameTable()) +
                getBaseQueryForTimeFrame(payload, metadata, tenantId, timeCondition.getComparisonTimeCondition(), "prev", derivedMetadata.getComparedTimeFrameTable()) +
                "let rows = " +
                " curr | join kind = leftouter(prev) on " + getGroupByFields(metadata.getGroupByFields()) +
                transformSortingToString(payload.getSortByFields()) +
                tranformPaginationtoString(payload.getPagination());
    }
    // top dest roles widget: Get max data by flows and bytes, return a union of the two. UI will show either based on selection
    private String getTopDestinationRoles(RequestContext requestContext, TimeCondition timeCondition, Metadata metadata) {
        SortByFields sortByField = SortByFields.builder().field(COUNT.getFieldKey()).order("desc").build();
        RequestPayload payload = requestContext.getRequestPayload().get();
        String tenantId = requestContext.getTenantId().get();
        DerivedMetadata derivedMetadata= requestContext.getDerivedMetadata().get();

        // TODO: This query runs the main selection 2 times for previous and current, we can extract that outside current and previous logic.
        return "let byFlows = " + getDirectionalBaseQuery(payload, tenantId, derivedMetadata.getCurrentTimeFrameTable(), timeCondition.getCurrentTimeCondition()) +
                transformGroupingToString(List.of(AGGREGATED_FLOWS), metadata.getGroupByFields(), Optional.of(COUNT.getFieldDisplayName())) +
                transformSortingToStringIntermediate(List.of(sortByField)) +
                String.format(QUERY_DATA_LIMIT, payload.getPagination() == null ? TOP_QUERIES_LIMIT : payload.getPagination().getRowLimit()) + ";" +

                "let byFlowsPrev = " + getDirectionalBaseQuery(payload, tenantId, derivedMetadata.getComparedTimeFrameTable(), timeCondition.getComparisonTimeCondition()) +
                "| where "+DESTINATION_ROLE.getTableColumnName()+" in(byFlows) " +
                transformGroupingToString(List.of(AGGREGATED_FLOWS), metadata.getGroupByFields(), Optional.of(COUNT.getFieldDisplayName())) + ";"+

                "let byBytes = " + getDirectionalBaseQuery(payload, tenantId, derivedMetadata.getCurrentTimeFrameTable(), timeCondition.getCurrentTimeCondition()) +
                transformGroupingToString(List.of(AGGREGATED_BYTES), metadata.getGroupByFields(), Optional.of(COUNT.getFieldDisplayName())) +
                transformSortingToStringIntermediate(List.of(sortByField)) +
                String.format(QUERY_DATA_LIMIT, payload.getPagination() == null ? TOP_QUERIES_LIMIT : payload.getPagination().getRowLimit()) + ";" +

                "let byBytesPrev = " + getDirectionalBaseQuery(payload, tenantId, derivedMetadata.getComparedTimeFrameTable(), timeCondition.getComparisonTimeCondition()) +
                "| where "+DESTINATION_ROLE.getTableColumnName()+" in(byBytes) " +
                transformGroupingToString(List.of(AGGREGATED_BYTES), metadata.getGroupByFields(), Optional.of(COUNT.getFieldDisplayName())) + ";" +

                generateJoinQuery("finalFlows", "byFlows", "byFlowsPrev", DESTINATION_ROLE.getTableColumnName(), "FLOWS") +
                generateJoinQuery("finalBytes", "byBytes", "byBytesPrev", DESTINATION_ROLE.getTableColumnName(), "BYTES") +

                String.format(UNION_QUERY, "finalFlows", "finalBytes");
    }

    // top source roles widget: Get max data by flows and bytes, return a union of the two. UI will show either based on selection
    private String getTopSourceRoles(RequestContext requestContext, TimeCondition timeCondition, Metadata metadata) {
        SortByFields sortByField = SortByFields.builder().field(COUNT.getFieldKey()).order("desc").build();
        RequestPayload payload = requestContext.getRequestPayload().get();
        String tenantId = requestContext.getTenantId().get();
        DerivedMetadata derivedMetadata = requestContext.getDerivedMetadata().get();

        return "let byFlows = " +
                getDirectionalBaseQuery(payload, tenantId, derivedMetadata.getCurrentTimeFrameTable(), timeCondition.getCurrentTimeCondition()) +
                transformGroupingToString(List.of(AGGREGATED_FLOWS), metadata.getGroupByFields(), Optional.of(COUNT.getFieldDisplayName())) +
                transformSortingToStringIntermediate(List.of(sortByField)) +
                String.format(QUERY_DATA_LIMIT, payload.getPagination() == null ? TOP_QUERIES_LIMIT : payload.getPagination().getRowLimit()) + ";" +
                "let byFlowsPrev = " +
                getDirectionalBaseQuery(payload, tenantId, derivedMetadata.getComparedTimeFrameTable(), timeCondition.getComparisonTimeCondition()) +
                "| where " + SOURCE_ROLE.getTableColumnName() + " in(byFlows) " +
                transformGroupingToString(List.of(AGGREGATED_FLOWS), metadata.getGroupByFields(), Optional.of(COUNT.getFieldDisplayName())) + ";" +

                "let byBytes = " +
                getDirectionalBaseQuery(payload, tenantId, derivedMetadata.getCurrentTimeFrameTable(), timeCondition.getCurrentTimeCondition()) +
                transformGroupingToString(List.of(AGGREGATED_BYTES), metadata.getGroupByFields(), Optional.of(COUNT.getFieldDisplayName())) +
                transformSortingToStringIntermediate(List.of(sortByField)) +
                String.format(QUERY_DATA_LIMIT, payload.getPagination() == null ? TOP_QUERIES_LIMIT : payload.getPagination().getRowLimit()) + ";" +
                "let byBytesPrev = " +
                getDirectionalBaseQuery(payload, tenantId, derivedMetadata.getComparedTimeFrameTable(), timeCondition.getComparisonTimeCondition()) +
                "| where " + SOURCE_ROLE.getTableColumnName() + " in(byBytes) " +
                transformGroupingToString(List.of(AGGREGATED_BYTES), metadata.getGroupByFields(), Optional.of(COUNT.getFieldDisplayName())) + ";" +

                generateJoinQuery("finalFlows", "byFlows", "byFlowsPrev", SOURCE_ROLE.getTableColumnName(), "FLOWS") +
                generateJoinQuery("finalBytes", "byBytes", "byBytesPrev", SOURCE_ROLE.getTableColumnName(), "BYTES") +
                String.format(UNION_QUERY, "finalFlows", "finalBytes");
    }

    // top dest roles widget: Get max data by flows and bytes, return a union of the two. UI will show either based on selection
    // after getting max data, we also need to get the time-series data so it can be plotted across a graph
    private String getTopWorkloads(RequestContext requestContext, TimeCondition timeCondition, Metadata metadata) {
        RequestPayload payload = requestContext.getRequestPayload().get();
        String tenantId = requestContext.getTenantId().get();
        DerivedMetadata derivedMetadata= requestContext.getDerivedMetadata().get();

        // For all line charts, the aggregate window is set to 1 hour. Because of this, the time window needs to be updated to read from Hourly Table always
        timeCondition.setCurrentTimeCondition("| where todatetime(StartTime) >= datetime('" + derivedMetadata.getUpdatedCurrentTimeFrame().getStartTime() +
                "') | where todatetime(EndTime) <= datetime('" + derivedMetadata.getUpdatedCurrentTimeFrame().getEndTime() + "')");
        timeCondition.setComparisonTimeCondition("| where todatetime(StartTime) >= datetime('" + derivedMetadata.getUpdatedComparisonTimeFrame().getStartTime() +
                "') | where todatetime(EndTime) <= datetime('" + derivedMetadata.getUpdatedComparisonTimeFrame().getEndTime() + "')");
        String table = "Insights_" + metadata.getTableName() + "_Hourly";

        return "let byFlows = " + getBaseQuery(payload, tenantId, table, timeCondition.getCurrentTimeCondition()) +
                " | where isnotempty(SrcResId) " +
                transformGroupingToString(List.of(AGGREGATED_FLOWS), metadata.getGroupByFields(), Optional.of(COUNT.getFieldDisplayName())) +
                "| top " + (payload.getPagination() == null ? TOP_QUERIES_LIMIT : payload.getPagination().getRowLimit()) + " by " + COUNT.getTableColumnName() + " ; " +

                "let byBytes = " + getBaseQuery(payload, tenantId, table, timeCondition.getCurrentTimeCondition()) +
                " | where isnotempty(SrcResId) " +
                transformGroupingToString(List.of(AGGREGATED_BYTES), metadata.getGroupByFields(), Optional.of(COUNT.getFieldDisplayName())) +
                "| top " + (payload.getPagination() == null ? TOP_QUERIES_LIMIT : payload.getPagination().getRowLimit()) + " by " + COUNT.getTableColumnName() + " ; " +

                "let topWByFlows = " + getBaseQuery(payload, tenantId, table, timeCondition.getCurrentTimeCondition()) +
                "| where SrcResId in ( byFlows | project SrcResId ) " +
                transformGroupingToString(List.of(AGGREGATED_FLOWS), metadata.getGroupByFields(), Optional.of(COUNT.getFieldDisplayName())) +
                ", TimeSeries = bin("+Fields.START_TIME.getTableColumnName()+", 1h)" +
                makeTimeSeriesCustom(COUNT.getFieldDisplayName(), derivedMetadata.getUpdatedCurrentTimeFrame(), 0, "SrcResId, SrcResourceName, SrcResourceCategory, SrcResourceType") +
                String.format(EXTEND_AGGREGATE_FIELD, AGGREGATE_FIELD.getTableColumnName(), "Flows") + " ; " +

                "let topWByBytes = " + getBaseQuery(payload, tenantId, table, timeCondition.getCurrentTimeCondition()) +
                "| where SrcResId in ( byBytes | project SrcResId ) " +
                transformGroupingToString(List.of(AGGREGATED_BYTES), metadata.getGroupByFields(), Optional.of(COUNT.getFieldDisplayName())) +
                ", TimeSeries = bin("+Fields.START_TIME.getTableColumnName()+", 1h)" +
                makeTimeSeriesCustom(COUNT.getFieldDisplayName(), derivedMetadata.getUpdatedCurrentTimeFrame(), 0, "SrcResId, SrcResourceName, SrcResourceCategory, SrcResourceType") +
                String.format(EXTEND_AGGREGATE_FIELD, AGGREGATE_FIELD.getTableColumnName(), "Bytes") + " ; " +

                String.format(UNION_QUERY, "topWByFlows", "topWByBytes");
    }
    // dest role traffic widget: Join current timeframe with previous -> sort the data -> paginate and return
    private String getDestRoleTrafficQuery(RequestContext requestContext, TimeCondition timeCondition, Metadata metadata) {
        RequestPayload payload = requestContext.getRequestPayload().get();
        String tenantId = requestContext.getTenantId().get();
        DerivedMetadata derivedMetadata= requestContext.getDerivedMetadata().get();

        return getBaseQueryForTimeFrame(payload, metadata, tenantId, timeCondition.getCurrentTimeCondition(), "curr", derivedMetadata.getCurrentTimeFrameTable()) +
                getBaseQueryForTimeFrame(payload, metadata, tenantId, timeCondition.getComparisonTimeCondition(), "prev", derivedMetadata.getComparedTimeFrameTable()) +
                "let rows = " +
                " curr | join kind = leftouter(prev) on " + getGroupByFields(metadata.getGroupByFields()) +
                " | where isnotempty( SrcResId) " +
                transformSortingToString(payload.getSortByFields()) +
                tranformPaginationtoString(payload.getPagination());
    }

    /*
     2. Queries for Malicious IP Page
     */

    private String getMalQueryGeneric(RequestContext requestContext, Fields aggrField, String summarize, String orderBy, String joinOnField, String filterField, TimeCondition timeCondition, Metadata metadata) {

        RequestPayload payload = requestContext.getRequestPayload().orElseThrow();
        String tenantId = requestContext.getTenantId().orElseThrow();
        DerivedMetadata derivedMetadata = requestContext.getDerivedMetadata().orElseThrow();

        String direction = payload.getFilters() == null ? null : payload.getFilters().stream()
                .filter(f -> "traffic_direction".equalsIgnoreCase(f.getCategoryName()))
                .flatMap(f -> f.getCategoryValue().stream())
                .filter(val -> val instanceof String)
                .map(val -> ((String) val).toUpperCase())
                .findFirst()
                .orElse(null);

        //TODO: UI Should send this
        if(direction == null) {
            direction = "INBOUND";
        }

        Map<String, Fields> fields = Fields.getDirectionBoundFields().get(direction);

        String topByAggrField = "topBy" + aggrField.getFieldKey();
        String prevByAggrField = "prevBy" + aggrField.getFieldKey();

        return  // Query for current timeframe aggr field
                " let " + topByAggrField + " = "
                        + derivedMetadata.getCurrentTimeFrameTable() + " "
                        + String.format(TENANT_ID, tenantId)
                        + maliciousIpBaseCheck(direction, timeCondition.getCurrentTimeCondition(), payload)
                        + summarize
                        + orderBy
                        + String.format(QUERY_DATA_LIMIT, payload.getPagination() == null ? TOP_QUERIES_LIMIT : payload.getPagination().getRowLimit()) + " ; "
                        // Query for previous timeframe aggr field
                        + " let " + prevByAggrField + " = "
                        + derivedMetadata.getComparedTimeFrameTable() + " "
                        + String.format(TENANT_ID, tenantId)
                        + maliciousIpBaseCheck(direction, timeCondition.getComparisonTimeCondition(), payload)
                        + " | where " + fields.get(filterField).getTableColumnName() + " in (" + topByAggrField + ")"
                        + summarize + " ; "
                        // Join timeframes
                        + " let " + aggrField.getFieldKey() + " = " + topByAggrField
                        + "| join kind=leftouter ("+ prevByAggrField +") on " + joinOnField
                        + String.format(EXTEND_AGGREGATE_FIELD, AGGREGATE_FIELD.getTableColumnName(), aggrField.getFieldKey())
                        + orderBy + " ; ";
    }

    private String maliciousIpBaseCheck(String direction, String timeCondition, RequestPayload payload) {
        Map<String, Fields> fields = Fields.getDirectionBoundFields().get(direction);
        String isAllowed = payload.getFilters() == null ? null : payload.getFilters().stream()
                .filter(f -> "traffic_status".equalsIgnoreCase(f.getCategoryName()))
                .flatMap(f -> f.getCategoryValue().stream())
                .filter(val -> val instanceof String)
                .map(val -> ((String) val).toUpperCase())
                .findFirst()
                .orElse(null);
        String query =  " | where isempty( " + fields.get("RESOURCEID").getTableColumnName() + " ) and isnotempty( " + fields.get("COUNTERRESOURCEID").getTableColumnName() + " )"
                + " | where " + fields.get("THREATLEVEL").getTableColumnName() + " >= 2 ";

        if(isAllowed != null) {
            query += " | where TrafficStatus in ('" + isAllowed + "')";
        }

        query += timeCondition;

        return query;
    }

    // dest role traffic widget: Join current timeframe with previous -> sort the data -> paginate and return
    private String getThreatMap(RequestContext requestContext, TimeCondition timeCondition, Metadata metadata) {
        RequestPayload payload = requestContext.getRequestPayload().orElseThrow();

        String direction = payload.getFilters() == null ? null : payload.getFilters().stream()
                .filter(f -> "traffic_direction".equalsIgnoreCase(f.getCategoryName()))
                .flatMap(f -> f.getCategoryValue().stream())
                .filter(val -> val instanceof String)
                .map(val -> ((String) val).toUpperCase())
                .findFirst()
                .orElse(null);

        //TODO: UI should send this
        if(direction == null) {
            direction = "INBOUND";
        }

        Map<String, Fields> fields = Fields.getDirectionBoundFields().get(direction);
        String summarizeFlows = " | summarize Count = sum( " + AGGREGATED_FLOWS.getTableColumnName() + " ), ThreatLevel = max( " + fields.get("THREATLEVEL").getTableColumnName() + " ) by Country= " + fields.get("COUNTRY").getTableColumnName();
        String summarizeBytes = " | summarize Count = sum( " + AGGREGATED_BYTES.getTableColumnName() + " ), ThreatLevel = max( " + fields.get("THREATLEVEL").getTableColumnName() + " ) by Country= " + fields.get("COUNTRY").getTableColumnName();
        String orderBy = " | order by ThreatLevel desc, Count desc";
        String joinOnField = COUNTRY.getTableColumnName();

        String flowQuery = getMalQueryGeneric(requestContext, AGGREGATED_FLOWS, summarizeFlows, orderBy, joinOnField, "COUNTRY", timeCondition, metadata);
        String bytesQuery = getMalQueryGeneric(requestContext, AGGREGATED_BYTES, summarizeBytes, orderBy, joinOnField, "COUNTRY", timeCondition, metadata);
        String unionQuery = "union flows, bytes";

        // Return the complete threat map query
        return flowQuery + " "
                + bytesQuery + " "
                + unionQuery;
    }

    // top roles widget: Get max data by flows and bytes, return a union of the two. UI will show either based on selection
    private String getTopRoles(RequestContext requestContext, TimeCondition timeCondition, Metadata metadata) {
        RequestPayload payload = requestContext.getRequestPayload().orElseThrow();

        String direction = payload.getFilters() == null ? null : payload.getFilters().stream()
                .filter(f -> "traffic_direction".equalsIgnoreCase(f.getCategoryName()))
                .flatMap(f -> f.getCategoryValue().stream())
                .filter(val -> val instanceof String)
                .map(val -> ((String) val).toUpperCase())
                .findFirst()
                .orElse(null);

        //TODO: UI should send this
        if(direction == null)
            direction = "INBOUND";

        Map<String, Fields> fields = Fields.getDirectionBoundFields().get(direction);
        String summarizeFlows = " | summarize Count = sum( " + AGGREGATED_FLOWS.getTableColumnName() + " ) by Role= " + fields.get("ROLE").getTableColumnName();
        String summarizeBytes = " | summarize Count = sum( " + AGGREGATED_BYTES.getTableColumnName() + " ) by Role= " + fields.get("ROLE").getTableColumnName();
        String orderBy = " | order by Count desc";
        String joinOnField = ROLE.getTableColumnName();

        String flowQuery = getMalQueryGeneric(requestContext, AGGREGATED_FLOWS, summarizeFlows, orderBy, joinOnField, "ROLE", timeCondition, metadata);
        String bytesQuery = getMalQueryGeneric(requestContext, AGGREGATED_BYTES, summarizeBytes, orderBy, joinOnField, "ROLE", timeCondition, metadata);
        String unionQuery = "union flows, bytes";

        // Return the complete query
        return flowQuery + " "
                + bytesQuery + " "
                + unionQuery;
    }

    // top roles widget: Get max data by flows and bytes, return a union of the two. UI will show either based on selection
    private String getTopServices(RequestContext requestContext, TimeCondition timeCondition, Metadata metadata) {
        RequestPayload payload = requestContext.getRequestPayload().orElseThrow();

        String direction = payload.getFilters() == null ? null : payload.getFilters().stream()
                .filter(f -> "traffic_direction".equalsIgnoreCase(f.getCategoryName()))
                .flatMap(f -> f.getCategoryValue().stream())
                .filter(val -> val instanceof String)
                .map(val -> ((String) val).toUpperCase())
                .findFirst()
                .orElse(null);

        //TODO: UI should send this
        if(direction == null)
            direction = "INBOUND";

        Map<String, Fields> fields = Fields.getDirectionBoundFields().get(direction);
        String summarizeFlows = " | summarize Count = sum( " + AGGREGATED_FLOWS.getTableColumnName() + " ), ThreatLevel = max( " + fields.get("THREATLEVEL").getTableColumnName() + " ) by " + PORT.getTableColumnName() + ", " + PROTOCOL.getTableColumnName();
        String summarizeBytes = " | summarize Count = sum( " + AGGREGATED_BYTES.getTableColumnName() + " ), ThreatLevel = max( " + fields.get("THREATLEVEL").getTableColumnName() + " ) by " + PORT.getTableColumnName() + ", " + PROTOCOL.getTableColumnName();
        String orderBy = " | order by ThreatLevel desc, Count desc";
        String joinOnField = "Port, Proto";

        String flowQuery = getMalQueryGeneric(requestContext, AGGREGATED_FLOWS, summarizeFlows, orderBy, joinOnField, "PORT", timeCondition, metadata);
        String bytesQuery = getMalQueryGeneric(requestContext, AGGREGATED_BYTES, summarizeBytes, orderBy, joinOnField, "PORT", timeCondition, metadata);
        String unionQuery = "union flows, bytes";

        // Return the complete query
        return flowQuery + " "
                + bytesQuery + " "
                + unionQuery;
    }

    private String getTopCategoryWithMaliciousIPs(RequestContext requestContext, TimeCondition timeCondition, Metadata metadata) {
        String orderBy = " | order by Count desc";

        String flowQuery = getTopCategoryQuery(requestContext, AGGREGATED_FLOWS, orderBy, timeCondition, metadata);
        String bytesQuery = getTopCategoryQuery(requestContext, AGGREGATED_BYTES, orderBy, timeCondition, metadata);
        String unionQuery = "union flows, bytes";

        // Return the complete query
        return flowQuery + " "
                + bytesQuery + " "
                + unionQuery;
    }

    private String getTopCategoryQuery(RequestContext requestContext, Fields aggrField, String orderBy, TimeCondition timeCondition, Metadata metadata) {
        RequestPayload payload = requestContext.getRequestPayload().orElseThrow();
        String tenantId = requestContext.getTenantId().orElseThrow();
        DerivedMetadata derivedMetadata = requestContext.getDerivedMetadata().orElseThrow();

        String direction = payload.getFilters() == null ? null : payload.getFilters().stream()
                .filter(f -> "traffic_direction".equalsIgnoreCase(f.getCategoryName()))
                .flatMap(f -> f.getCategoryValue().stream())
                .filter(val -> val instanceof String)
                .map(val -> ((String) val).toUpperCase())
                .findFirst()
                .orElse(null);
        String category = payload.getFilters() == null ? null : payload.getFilters().stream()
                .filter(f -> "category".equalsIgnoreCase(f.getCategoryName()))
                .flatMap(f -> f.getCategoryValue().stream())
                .filter(val -> val instanceof String)
                .map(val -> ((String) val).toUpperCase())
                .findFirst()
                .orElse(null);

        // TODO: UI Should send this
        if(direction == null)
            direction = "INBOUND";
        if(category == null)
            category = "SUBSCRIPTIONS";

        String categoryId = Fields.getDirectionBoundFields().get(direction).get(category).getTableColumnName();
        String Ip = Fields.getDirectionBoundFields().get(direction).get("IP").getTableColumnName();
        String accountName = Fields.getDirectionBoundFields().get(direction).get("ACCOUNTNAME").getTableColumnName();
        String cloudProvider = Fields.getDirectionBoundFields().get(direction).get("CLOUDPROVIDER").getTableColumnName();

        String topByAggrField = "topBy" + aggrField.getFieldKey();
        String topIpByAggrField = "topIpBy" + aggrField.getFieldKey();
        String prevIpByAggrField = "prevIpBy" + aggrField.getFieldKey();

        return  // Query for current timeframe aggr field
                " let " + topByAggrField + " = "
                        + derivedMetadata.getCurrentTimeFrameTable() + " "
                        + String.format(TENANT_ID, tenantId)
                        + maliciousIpBaseCheck(direction, timeCondition.getCurrentTimeCondition(), payload)
                        + " | summarize Count = sum(" + aggrField.getTableColumnName() + ") by " + categoryId
                        + orderBy
                        + String.format(QUERY_DATA_LIMIT, payload.getPagination() == null ? TOP_QUERIES_LIMIT : payload.getPagination().getRowLimit()) + " ; "
                        + " let " + topIpByAggrField + " = "
                        + derivedMetadata.getCurrentTimeFrameTable() + " "
                        + String.format(TENANT_ID, tenantId)
                        + maliciousIpBaseCheck(direction, timeCondition.getCurrentTimeCondition(), payload)
                        + " | where " + categoryId + " in (" + topByAggrField + ")"
                        + " | summarize Count = sum(" + aggrField.getTableColumnName() + "), AccountName=take_anyif(" + accountName + ", isnotempty(" + accountName +")), "
                        + "CloudProvider=take_anyif(" + cloudProvider + ", isnotempty(" + cloudProvider +"))" + " by " + Ip + ", " + categoryId
                        + orderBy
                        + String.format(QUERY_DATA_LIMIT, payload.getPagination() == null ? TOP_QUERIES_LIMIT : payload.getPagination().getRowLimit()) + " ; "
                        // Query for previous timeframe aggr field
                        + " let " + prevIpByAggrField + " = "
                        + derivedMetadata.getComparedTimeFrameTable() + " "
                        + String.format(TENANT_ID, tenantId)
                        + maliciousIpBaseCheck(direction, timeCondition.getCurrentTimeCondition(), payload)
                        + " | where " + categoryId + " in (" + topIpByAggrField + " | project " + categoryId + ")"
                        + " | where " + Ip + " in (" + topIpByAggrField + " | project " + Ip + ")"
                        + " | summarize Count = sum(" + aggrField.getTableColumnName() + ") by " + categoryId + ", " + Ip + "; "
                        // Join timeframes
                        + " let " + aggrField.getFieldKey() + " = "
                        +  topIpByAggrField
                        + "| join kind=leftouter (" + prevIpByAggrField + ") on " + categoryId + ", " + Ip
                        + String.format(EXTEND_AGGREGATE_FIELD, AGGREGATE_FIELD.getTableColumnName(), aggrField.getFieldKey()) + ", CategoryId = " + categoryId + ", Ip = " + Ip
                        + orderBy + " ; ";

    }

    private String getTopMaliciousIps(RequestContext requestContext, TimeCondition timeCondition, Metadata metadata) {
        String orderBy = " | order by Count desc";

        String flowQuery = getTopMalIPQuery(requestContext, AGGREGATED_FLOWS, orderBy, timeCondition, metadata);
        String bytesQuery = getTopMalIPQuery(requestContext, AGGREGATED_BYTES, orderBy, timeCondition, metadata);
        String unionQuery = "union flows, bytes";

        // Return the complete query
        return flowQuery + " "
                + bytesQuery + " "
                + unionQuery;
    }

    private String getTopMalIPQuery(RequestContext requestContext, Fields aggrField, String orderBy, TimeCondition timeCondition, Metadata metadata) {
        RequestPayload payload = requestContext.getRequestPayload().orElseThrow();
        String tenantId = requestContext.getTenantId().orElseThrow();
        DerivedMetadata derivedMetadata = requestContext.getDerivedMetadata().orElseThrow();

        String direction = payload.getFilters() == null ? null : payload.getFilters().stream()
                .filter(f -> "traffic_direction".equalsIgnoreCase(f.getCategoryName()))
                .flatMap(f -> f.getCategoryValue().stream())
                .filter(val -> val instanceof String)
                .map(val -> ((String) val).toUpperCase())
                .findFirst()
                .orElse(null);

        //TODO: UI should send this
        if(direction == null) {
            direction = "INBOUND";
        }

        String Ip = Fields.getDirectionBoundFields().get(direction).get("IP").getTableColumnName();
        String ThreatLevel = Fields.getDirectionBoundFields().get(direction).get("THREATLEVEL").getTableColumnName();

        String topByAggrField = "topBy" + aggrField.getFieldKey();
        String topIpByAggrField = "topIpBy" + aggrField.getFieldKey();
        String prevIpByAggrField = "prevIpBy" + aggrField.getFieldKey();

        return  // Query for current timeframe aggr field
                " let " + topByAggrField + " = "
                        + derivedMetadata.getCurrentTimeFrameTable() + " "
                        + String.format(TENANT_ID, tenantId)
                        + maliciousIpBaseCheck(direction, timeCondition.getCurrentTimeCondition(), payload)
                        + " | summarize Count = sum(" + aggrField.getTableColumnName() + ") by " + Ip
                        + orderBy
                        + String.format(QUERY_DATA_LIMIT, payload.getPagination() == null ? TOP_QUERIES_LIMIT : payload.getPagination().getRowLimit()) + " ; "
                        + " let " + topIpByAggrField + " = "
                        + derivedMetadata.getCurrentTimeFrameTable() + " "
                        + String.format(TENANT_ID, tenantId)
                        + maliciousIpBaseCheck(direction, timeCondition.getCurrentTimeCondition(), payload)
                        + " | where " + Ip + " in (" + topByAggrField + "| project " + Ip +  ")"
                        + " | summarize Count = sum( " + aggrField.getTableColumnName() + " ), ThreatLevel = max( " + ThreatLevel + " ) by " + PORT.getTableColumnName() + ", " + PROTOCOL.getTableColumnName() + ", " + Ip
                        + orderBy
                        + " ; "
                        // Query for previous timeframe aggr field
                        + " let " + prevIpByAggrField + " = "
                        + derivedMetadata.getComparedTimeFrameTable() + " "
                        + String.format(TENANT_ID, tenantId)
                        + maliciousIpBaseCheck(direction, timeCondition.getCurrentTimeCondition(), payload)
                        + " | where " + Ip + " in (" + topByAggrField + "| project " + Ip +  ")"
                        + " | summarize Count = sum( " + aggrField.getTableColumnName() + " ), ThreatLevel = max( " + ThreatLevel + " ) by " + Ip + ";"
                        // Join timeframes
                        + " let " + aggrField.getFieldKey() + " = "
                        +  topIpByAggrField
                        + " | join kind=leftouter (" + prevIpByAggrField + ") on " + Ip
                        + String.format(EXTEND_AGGREGATE_FIELD, AGGREGATE_FIELD.getTableColumnName(), aggrField.getFieldKey()) + ", Ip = " + Ip
                        + " | order by " + COUNT.getTableColumnName()
                        + " ; ";
    }

    /*
     3. Queries for External Data Transfer Page
     */

    private String getExternalCategoryTransferQuery(RequestContext requestContext, TimeCondition timeCondition, Metadata metadata) {
        RequestPayload payload = requestContext.getRequestPayload().get();
        String tenantId = requestContext.getTenantId().get();
        DerivedMetadata derivedMetadata= requestContext.getDerivedMetadata().get();

        return getBaseQueryForTimeFrame(payload, metadata, tenantId, timeCondition.getCurrentTimeCondition(), "curr", derivedMetadata.getCurrentTimeFrameTable()) +
                getBaseQueryForTimeFrame(payload, metadata, tenantId, timeCondition.getComparisonTimeCondition(), "prev", derivedMetadata.getComparedTimeFrameTable()) +
                "let rows = " +
                " curr | join kind = leftouter(prev) on " + getGroupByFields(metadata.getGroupByFields()) +
                transformSortingToString(payload.getSortByFields()) +
                tranformPaginationtoString(payload.getPagination());
    }

    private String getExternalTransferByServiceQuery(RequestContext requestContext, TimeCondition timeCondition, Metadata metadata) {
        RequestPayload payload = requestContext.getRequestPayload().get();
        String tenantId = requestContext.getTenantId().get();
        DerivedMetadata derivedMetadata= requestContext.getDerivedMetadata().get();

        return getBaseQueryForTimeFrame(payload, metadata, tenantId, timeCondition.getCurrentTimeCondition(), "curr", derivedMetadata.getCurrentTimeFrameTable()) +
                getBaseQueryForTimeFrame(payload, metadata, tenantId, timeCondition.getComparisonTimeCondition(), "prev", derivedMetadata.getComparedTimeFrameTable()) +
                "let rows = " +
                " curr | join kind = leftouter(prev) on " + getGroupByFields(metadata.getGroupByFields()) +
                transformSortingToString(payload.getSortByFields()) +
                tranformPaginationtoString(payload.getPagination());
    }

    private String getExternalTransferGenericSelectionQuery(String timeCondition, String tableName, String tenantId, String label, String[] projectionFields, List<Filters> filters) {

        return "let "+label+" = "+tableName+
                timeCondition +
                String.format(TENANT_ID, tenantId) +
                transformFiltersToString(filters) +
                "| project " + String.join(", ", projectionFields) + ";";
    }

    private String getExternalTransferGenericCurrAggregationQuery(List<Fields> summarizeFields, List<Fields> groupByFields, String tableName, String label, int rowLimit, String summarizeLabel) {
        return "let "+label+" = "+tableName +
                transformGroupingToString(summarizeFields, groupByFields, Optional.of(summarizeLabel) )+
                "| top "+ rowLimit+" by "+ summarizeLabel+" desc;";
    }

    private String getExternalTransferGenericPrevAggregationQuery(List<Fields> summarizeFields, List<Fields> groupByFields, String filterColumn, String value, String tableName, String label, String summarizeLabel) {
        return "let "+label+" = "+tableName +
                "| where " +filterColumn+" in ("+value+")" +
                transformGroupingToString(summarizeFields, groupByFields, Optional.of(summarizeLabel)) + ";";
    }

    private String getExternalTransferTimeSeriesQuery(String label, String tableName, String filterColumn, String value, List<Fields> summarizeFields, List<Fields> groupByFields, TimeFrame timeFrame, String aggField) {
        String groupBy = groupByFields.stream()
                        .map(Fields::getTableColumnName)
                        .collect(Collectors.joining(","));
        return "let "+label+" = "+tableName +
                "| where " +filterColumn+" in ("+value+")" +
                transformGroupingToString(summarizeFields, groupByFields, Optional.of(COUNT.getFieldDisplayName())) +
                ", TimeSeries = bin("+Fields.START_TIME.getTableColumnName()+", 1h)" +
                makeTimeSeriesCustom(COUNT.getFieldDisplayName(), timeFrame, 0, groupBy) +
                String.format(EXTEND_AGGREGATE_FIELD, AGGREGATE_FIELD.getTableColumnName(), aggField) + " ; ";
    }

    private String getExternalTransferGenericJoinQuery(String tableName, String label, String prevTableName, String joinKey, String aggField, String prevValue, String curValue) {
        return "let "+label+" = "+tableName +
                "| join kind=leftouter("+prevTableName+") on "+joinKey +
                "| project "+joinKey+"=coalesce("+joinKey+", \"Unknown\"), "+AGGREGATE_FIELD.getTableColumnName()+"='"+aggField+"', "+PREVIOUS_COUNT.getTableColumnName()+"= coalesce("+prevValue+",0), "+COUNT.getTableColumnName()+"= "+curValue+";";

    }

    private String getTopSourceRoleTransferQuery(RequestContext requestContext, TimeCondition timeCondition, Metadata metadata) {
        RequestPayload payload = requestContext.getRequestPayload().get();
        String tenantId = requestContext.getTenantId().get();
        DerivedMetadata derivedMetadata= requestContext.getDerivedMetadata().get();
        List<Filters> filters = payload.getFilters();

        String dataSelectionCurr = getExternalTransferGenericSelectionQuery(
                timeCondition.getCurrentTimeCondition(),
                derivedMetadata.getCurrentTimeFrameTable(),
                tenantId,
                "curr",
                new String[]{SOURCE_ROLE.getTableColumnName(), AGGREGATED_FLOWS.getTableColumnName(), AGGREGATED_BYTES.getTableColumnName()},
                filters
                );
        String dataSelectionPrev = getExternalTransferGenericSelectionQuery(
                timeCondition.getComparisonTimeCondition(),
                derivedMetadata.getComparedTimeFrameTable(),
                tenantId,
                "prev",
                new String[]{SOURCE_ROLE.getTableColumnName(), AGGREGATED_FLOWS.getTableColumnName(), AGGREGATED_BYTES.getTableColumnName()},
                filters
                );
        String currFlows = getExternalTransferGenericCurrAggregationQuery(
                List.of(AGGREGATED_FLOWS),
                List.of(SOURCE_ROLE),
                "curr",
                "currFlows",
                payload.getPagination().getRowLimit(),
                "TotalFlowCount"
        );
        String currBytes = getExternalTransferGenericCurrAggregationQuery(
                List.of(AGGREGATED_BYTES),
                List.of(SOURCE_ROLE),
                "curr",
                "currBytes",
                payload.getPagination().getRowLimit(),
                "TotalByteCount"
        );
        String prevFlows = getExternalTransferGenericPrevAggregationQuery(
                List.of(AGGREGATED_FLOWS),
                List.of(SOURCE_ROLE),
                SOURCE_ROLE.getTableColumnName(),
                "currFlows",
                "prev",
                "prevFlows",
                "TotalFlowCountPrev"
        );
        String prevBytes = getExternalTransferGenericPrevAggregationQuery(
                List.of(AGGREGATED_BYTES),
                List.of(SOURCE_ROLE),
                SOURCE_ROLE.getTableColumnName(),
                "currBytes",
                "prev",
                "prevBytes",
                "TotalByteCountPrev"
        );
        String finalFlows = getExternalTransferGenericJoinQuery(
                "currFlows",
                "Flows",
                "prevFlows",
                SOURCE_ROLE.getTableColumnName(),
                "FLOWS",
                "TotalFlowCountPrev",
                "TotalFlowCount"
        );
        String finalBytes = getExternalTransferGenericJoinQuery(
                "currBytes",
                "Bytes",
                "prevBytes",
                SOURCE_ROLE.getTableColumnName(),
                "BYTES",
                "TotalByteCountPrev",
                "TotalByteCount"
        );

        return  dataSelectionCurr +
                currFlows +
                currBytes +
                dataSelectionPrev +
                prevFlows+
                prevBytes+
                finalFlows+
                finalBytes+
                String.format(UNION_QUERY,"Flows", "Bytes");
    }

    private String getExternalGeoTransferQuery(RequestContext requestContext, TimeCondition timeCondition, Metadata metadata) {
        RequestPayload payload = requestContext.getRequestPayload().get();
        String tenantId = requestContext.getTenantId().get();
        DerivedMetadata derivedMetadata = requestContext.getDerivedMetadata().get();
        List<Filters> filters = payload.getFilters();

        String dataSelectionCurr = getExternalTransferGenericSelectionQuery(
               timeCondition.getCurrentTimeCondition(),
                derivedMetadata.getCurrentTimeFrameTable(),
                tenantId,
                "curr",
                new String[]{DESTINATION_COUNTRY.getTableColumnName(), AGGREGATED_FLOWS.getTableColumnName(), AGGREGATED_BYTES.getTableColumnName()},
                filters
        );
        String dataSelectionPrev = getExternalTransferGenericSelectionQuery(
                timeCondition.getComparisonTimeCondition(),
                derivedMetadata.getComparedTimeFrameTable(),
                tenantId,
                "prev",
                new String[]{DESTINATION_COUNTRY.getTableColumnName(), AGGREGATED_FLOWS.getTableColumnName(), AGGREGATED_BYTES.getTableColumnName()},
                filters
        );
        String currFlows = getExternalTransferGenericCurrAggregationQuery(
                List.of(AGGREGATED_FLOWS),
                List.of(DESTINATION_COUNTRY),
                "curr",
                "currFlows",
                payload.getPagination().getRowLimit(),
                "TotalFlowCount"
        );
        String currBytes = getExternalTransferGenericCurrAggregationQuery(
                List.of(AGGREGATED_BYTES),
                List.of(DESTINATION_COUNTRY),
                "curr",
                "currBytes",
                payload.getPagination().getRowLimit(),
                "TotalByteCount"
        );
        String prevFlows = getExternalTransferGenericPrevAggregationQuery(
                List.of(AGGREGATED_FLOWS),
                List.of(DESTINATION_COUNTRY),
                DESTINATION_COUNTRY.getTableColumnName(),
                "currFlows",
                "prev",
                "prevFlows",
                "TotalFlowCountPrev"
        );
        String prevBytes = getExternalTransferGenericPrevAggregationQuery(
                List.of(AGGREGATED_BYTES),
                List.of(DESTINATION_COUNTRY),
                DESTINATION_COUNTRY.getTableColumnName(),
                "currBytes",
                "prev",
                "prevBytes",
                "TotalByteCountPrev"
        );
        String finalFlows = getExternalTransferGenericJoinQuery(
                "currFlows",
                "Flows",
                "prevFlows",
                DESTINATION_COUNTRY.getTableColumnName(),
                "FLOWS",
                "TotalFlowCountPrev",
                "TotalFlowCount"
        );
        String finalBytes = getExternalTransferGenericJoinQuery(
                "currBytes",
                "Bytes",
                "prevBytes",
                DESTINATION_COUNTRY.getTableColumnName(),
                "BYTES",
                "TotalByteCountPrev",
                "TotalByteCount"
        );

        return  dataSelectionCurr +
                currFlows +
                currBytes +
                dataSelectionPrev +
                prevFlows+
                prevBytes+
                finalFlows+
                finalBytes+
                String.format(UNION_QUERY,"Flows", "Bytes");
    }

    private String getIpDataTransferInbound(RequestContext requestContext, TimeCondition timeCondition, Metadata metadata) {
        RequestPayload payload = requestContext.getRequestPayload().get();
        String tenantId = requestContext.getTenantId().get();
        DerivedMetadata derivedMetadata= requestContext.getDerivedMetadata().get();
        SortByFields sortByField = SortByFields.builder().field(COUNT.getFieldKey()).order("desc").build();

        // For all line charts, the aggregate window is set to 1 hour. Because of this, the time window needs to be updated to read from Hourly Table always
        timeCondition.setCurrentTimeCondition("| where todatetime(StartTime) >= datetime('" + derivedMetadata.getUpdatedCurrentTimeFrame().getStartTime() +
                "') | where todatetime(EndTime) <= datetime('" + derivedMetadata.getUpdatedCurrentTimeFrame().getEndTime() + "')");
        timeCondition.setComparisonTimeCondition("| where todatetime(StartTime) >= datetime('" + derivedMetadata.getUpdatedComparisonTimeFrame().getStartTime() +
                "') | where todatetime(EndTime) <= datetime('" + derivedMetadata.getUpdatedComparisonTimeFrame().getEndTime() + "')");
        String table = "Insights_" + metadata.getTableName() + "_Hourly";

        String inboundByBytes = "let byBytes = " + getDirectionalBaseQuery(payload, tenantId, table, timeCondition.getCurrentTimeCondition()) +
                transformGroupingToString(List.of(AGGREGATED_BYTES), metadata.getGroupByFields(), Optional.of(COUNT.getFieldDisplayName())) +
                transformSortingToStringIntermediate(List.of(sortByField)) +
                String.format(QUERY_DATA_LIMIT, payload.getPagination() == null ? TOP_QUERIES_LIMIT : payload.getPagination().getRowLimit()) + ";";
        String inboundByFlows = "let byFlows = " + getDirectionalBaseQuery(payload, tenantId, table, timeCondition.getCurrentTimeCondition()) +
                transformGroupingToString(List.of(AGGREGATED_FLOWS), metadata.getGroupByFields(), Optional.of(COUNT.getFieldDisplayName())) +
                transformSortingToStringIntermediate(List.of(sortByField)) +
                String.format(QUERY_DATA_LIMIT, payload.getPagination() == null ? TOP_QUERIES_LIMIT : payload.getPagination().getRowLimit()) + ";";


        String flowsTimeSeries = "let FlowsTimeSeries = " + getDirectionalBaseQuery(payload, tenantId, table, timeCondition.getCurrentTimeCondition()) +
                transformGroupingToString(List.of(AGGREGATED_FLOWS), metadata.getGroupByFields(), Optional.of(COUNT.getFieldDisplayName())) +
                ", TimeSeries = bin("+Fields.START_TIME.getTableColumnName()+", 1h)" +
                makeTimeSeries(COUNT.getFieldDisplayName(), derivedMetadata.getUpdatedCurrentTimeFrame(), 0) +
                String.format(EXTEND_AGGREGATE_FIELD, AGGREGATE_FIELD.getTableColumnName(), "FLOWS") + " ; ";

        String bytesTimeSeries = "let BytesTimeSeries = " + getDirectionalBaseQuery(payload, tenantId, table, timeCondition.getCurrentTimeCondition()) +
                transformGroupingToString(List.of(AGGREGATED_BYTES), metadata.getGroupByFields(), Optional.of(COUNT.getFieldDisplayName())) +
                ", TimeSeries = bin("+Fields.START_TIME.getTableColumnName()+", 1h)" +
                makeTimeSeries(COUNT.getFieldDisplayName(), derivedMetadata.getUpdatedCurrentTimeFrame(), 0) +
                String.format(EXTEND_AGGREGATE_FIELD, AGGREGATE_FIELD.getTableColumnName(), "BYTES") + ";";

        return inboundByBytes +
                inboundByFlows +
                flowsTimeSeries +
                bytesTimeSeries +
                String.format(UNION_QUERY, "FlowsTimeSeries", "BytesTimeSeries");
    }

    private String getIpDataTransferOutbound(RequestContext requestContext, TimeCondition timeCondition, Metadata metadata) {
        RequestPayload payload = requestContext.getRequestPayload().get();
        String tenantId = requestContext.getTenantId().get();
        DerivedMetadata derivedMetadata= requestContext.getDerivedMetadata().get();
        SortByFields sortByField = SortByFields.builder().field(COUNT.getFieldKey()).order("desc").build();

        // For all line charts, the aggregate window is set to 1 hour. Because of this, the time window needs to be updated to read from Hourly Table always
        timeCondition.setCurrentTimeCondition("| where todatetime(StartTime) >= datetime('" + derivedMetadata.getUpdatedCurrentTimeFrame().getStartTime() +
                "') | where todatetime(EndTime) <= datetime('" + derivedMetadata.getUpdatedCurrentTimeFrame().getEndTime() + "')");
        timeCondition.setComparisonTimeCondition("| where todatetime(StartTime) >= datetime('" + derivedMetadata.getUpdatedComparisonTimeFrame().getStartTime() +
                "') | where todatetime(EndTime) <= datetime('" + derivedMetadata.getUpdatedComparisonTimeFrame().getEndTime() + "')");
        String table = "Insights_" + metadata.getTableName() + "_Hourly";

        String outboundByBytes = "let byBytes = " + getDirectionalBaseQuery(payload, tenantId, table, timeCondition.getCurrentTimeCondition()) +
                transformGroupingToString(List.of(AGGREGATED_BYTES), metadata.getGroupByFields(), Optional.of(COUNT.getFieldDisplayName())) +
                transformSortingToStringIntermediate(List.of(sortByField)) +
                String.format(QUERY_DATA_LIMIT, payload.getPagination() == null ? TOP_QUERIES_LIMIT : payload.getPagination().getRowLimit()) + ";";
        String outboundByFlows = "let byFlows = " + getDirectionalBaseQuery(payload, tenantId, table, timeCondition.getCurrentTimeCondition()) +
                transformGroupingToString(List.of(AGGREGATED_FLOWS), metadata.getGroupByFields(), Optional.of(COUNT.getFieldDisplayName())) +
                transformSortingToStringIntermediate(List.of(sortByField)) +
                String.format(QUERY_DATA_LIMIT, payload.getPagination() == null ? TOP_QUERIES_LIMIT : payload.getPagination().getRowLimit()) + ";";


        String flowsTimeSeries = "let FlowsTimeSeries = " + getDirectionalBaseQuery(payload, tenantId, table, timeCondition.getCurrentTimeCondition()) +
                transformGroupingToString(List.of(AGGREGATED_FLOWS), metadata.getGroupByFields(), Optional.of(COUNT.getFieldDisplayName())) +
                ", TimeSeries = bin("+Fields.START_TIME.getTableColumnName()+", 1h)" +
                makeTimeSeriesCustom(COUNT.getFieldDisplayName(), derivedMetadata.getUpdatedCurrentTimeFrame(), 0, "DestIP") +
                String.format(EXTEND_AGGREGATE_FIELD, AGGREGATE_FIELD.getTableColumnName(), "FLOWS") + " ; ";

        String bytesTimeSeries = "let BytesTimeSeries = " + getDirectionalBaseQuery(payload, tenantId, table, timeCondition.getCurrentTimeCondition()) +
                transformGroupingToString(List.of(AGGREGATED_BYTES), metadata.getGroupByFields(), Optional.of(COUNT.getFieldDisplayName())) +
                ", TimeSeries = bin("+Fields.START_TIME.getTableColumnName()+", 1h)" +
                makeTimeSeriesCustom(COUNT.getFieldDisplayName(), derivedMetadata.getUpdatedCurrentTimeFrame(), 0, "DestIP") +
                String.format(EXTEND_AGGREGATE_FIELD, AGGREGATE_FIELD.getTableColumnName(), "BYTES") + ";";

        return outboundByBytes +
                outboundByFlows +
                flowsTimeSeries +
                bytesTimeSeries +
                String.format(UNION_QUERY, "FlowsTimeSeries", "BytesTimeSeries");
    }

    private String getTopSourceResources(RequestContext requestContext, TimeCondition timeCondition, Metadata metadata) {
        RequestPayload payload = requestContext.getRequestPayload().get();
        String tenantId = requestContext.getTenantId().get();
        DerivedMetadata derivedMetadata= requestContext.getDerivedMetadata().get();
        SortByFields sortByField = SortByFields.builder().field(COUNT.getFieldKey()).order("desc").build();

        // For all line charts, the aggregate window is set to 1 hour. Because of this, the time window needs to be updated to read from Hourly Table always
        timeCondition.setCurrentTimeCondition("| where todatetime(StartTime) >= datetime('" + derivedMetadata.getUpdatedCurrentTimeFrame().getStartTime() +
                "') | where todatetime(EndTime) <= datetime('" + derivedMetadata.getUpdatedCurrentTimeFrame().getEndTime() + "')");
        timeCondition.setComparisonTimeCondition("| where todatetime(StartTime) >= datetime('" + derivedMetadata.getUpdatedComparisonTimeFrame().getStartTime() +
                "') | where todatetime(EndTime) <= datetime('" + derivedMetadata.getUpdatedComparisonTimeFrame().getEndTime() + "')");
        String table = "Insights_" + metadata.getTableName() + "_Hourly";

        String topDestResByBytes = "let byBytes = " + getDirectionalBaseQuery(payload, tenantId, table, timeCondition.getCurrentTimeCondition()) +
                transformGroupingToString(List.of(AGGREGATED_BYTES), metadata.getGroupByFields(), Optional.of(COUNT.getFieldDisplayName())) +
                transformSortingToStringIntermediate(List.of(sortByField)) +
                String.format(QUERY_DATA_LIMIT, payload.getPagination() == null ? TOP_QUERIES_LIMIT : payload.getPagination().getRowLimit()) + ";";
        String topDestResByFlows = "let byFlows = " + getDirectionalBaseQuery(payload, tenantId, table, timeCondition.getCurrentTimeCondition()) +
                transformGroupingToString(List.of(AGGREGATED_FLOWS), metadata.getGroupByFields(), Optional.of(COUNT.getFieldDisplayName())) +
                transformSortingToStringIntermediate(List.of(sortByField)) +
                String.format(QUERY_DATA_LIMIT, payload.getPagination() == null ? TOP_QUERIES_LIMIT : payload.getPagination().getRowLimit()) + ";";


        String flowsTimeSeries = "let FlowsTimeSeries = " + getDirectionalBaseQuery(payload, tenantId, table, timeCondition.getCurrentTimeCondition()) +
                "| where " + SOURCE_RESOURCE_ID.getTableColumnName() + " in(byFlows | project SrcResId) " +
                transformGroupingToString(List.of(AGGREGATED_FLOWS), metadata.getGroupByFields(), Optional.of(COUNT.getFieldDisplayName())) +
                ", TimeSeries = bin("+Fields.START_TIME.getTableColumnName()+", 1h)" +
                makeTimeSeriesCustom(COUNT.getFieldDisplayName(), derivedMetadata.getUpdatedCurrentTimeFrame(), 0, "SrcResId, SrcResourceName, SrcResourceCategory, SrcResourceType") +
                String.format(EXTEND_AGGREGATE_FIELD, AGGREGATE_FIELD.getTableColumnName(), "FLOWS") + " ; ";

        String bytesTimeSeries = "let BytesTimeSeries = " + getDirectionalBaseQuery(payload, tenantId, table, timeCondition.getCurrentTimeCondition()) +
                "| where " + SOURCE_RESOURCE_ID.getTableColumnName() + " in(byBytes | project SrcResId) " +
                transformGroupingToString(List.of(AGGREGATED_BYTES), metadata.getGroupByFields(), Optional.of(COUNT.getFieldDisplayName())) +
                ", TimeSeries = bin("+Fields.START_TIME.getTableColumnName()+", 1h)" +
                makeTimeSeriesCustom(COUNT.getFieldDisplayName(), derivedMetadata.getUpdatedCurrentTimeFrame(), 0, "SrcResId, SrcResourceName, SrcResourceCategory, SrcResourceType") +
                String.format(EXTEND_AGGREGATE_FIELD, AGGREGATE_FIELD.getTableColumnName(), "BYTES") + ";";

        return topDestResByBytes +
                topDestResByFlows +
                flowsTimeSeries +
                bytesTimeSeries +
                String.format(UNION_QUERY, "FlowsTimeSeries", "BytesTimeSeries");
    }

    private String getTopDestinationResources(RequestContext requestContext, TimeCondition timeCondition, Metadata metadata) {
        RequestPayload payload = requestContext.getRequestPayload().get();
        String tenantId = requestContext.getTenantId().get();
        DerivedMetadata derivedMetadata= requestContext.getDerivedMetadata().get();
        SortByFields sortByField = SortByFields.builder().field(COUNT.getFieldKey()).order("desc").build();

        // For all line charts, the aggregate window is set to 1 hour. Because of this, the time window needs to be updated to read from Hourly Table always
        timeCondition.setCurrentTimeCondition("| where todatetime(StartTime) >= datetime('" + derivedMetadata.getUpdatedCurrentTimeFrame().getStartTime() +
                "') | where todatetime(EndTime) <= datetime('" + derivedMetadata.getUpdatedCurrentTimeFrame().getEndTime() + "')");
        timeCondition.setComparisonTimeCondition("| where todatetime(StartTime) >= datetime('" + derivedMetadata.getUpdatedComparisonTimeFrame().getStartTime() +
                "') | where todatetime(EndTime) <= datetime('" + derivedMetadata.getUpdatedComparisonTimeFrame().getEndTime() + "')");
        String table = "Insights_" + metadata.getTableName() + "_Hourly";

        String topDestResByBytes = "let byBytes = " + getDirectionalBaseQuery(payload, tenantId, table, timeCondition.getCurrentTimeCondition()) +
                transformGroupingToString(List.of(AGGREGATED_BYTES), metadata.getGroupByFields(), Optional.of(COUNT.getFieldDisplayName())) +
                transformSortingToStringIntermediate(List.of(sortByField)) +
                String.format(QUERY_DATA_LIMIT, payload.getPagination() == null ? TOP_QUERIES_LIMIT : payload.getPagination().getRowLimit()) + ";";
        String topDestResByFlows = "let byFlows = " + getDirectionalBaseQuery(payload, tenantId, table, timeCondition.getCurrentTimeCondition()) +
                transformGroupingToString(List.of(AGGREGATED_FLOWS), metadata.getGroupByFields(), Optional.of(COUNT.getFieldDisplayName())) +
                transformSortingToStringIntermediate(List.of(sortByField)) +
                String.format(QUERY_DATA_LIMIT, payload.getPagination() == null ? TOP_QUERIES_LIMIT : payload.getPagination().getRowLimit()) + ";";


        String flowsTimeSeries = "let FlowsTimeSeries = " + getDirectionalBaseQuery(payload, tenantId, table, timeCondition.getCurrentTimeCondition()) +
                "| where " + DESTINATION_RESOURCE_ID.getTableColumnName() + " in(byFlows | project DestResId) " +
                transformGroupingToString(List.of(AGGREGATED_FLOWS), metadata.getGroupByFields(), Optional.of(COUNT.getFieldDisplayName())) +
                ", TimeSeries = bin("+Fields.START_TIME.getTableColumnName()+", 1h)" +
                makeTimeSeriesCustom(COUNT.getFieldDisplayName(), derivedMetadata.getUpdatedCurrentTimeFrame(), 0, "DestResId, DestResourceName, DestResourceCategory, DestResourceType") +
                String.format(EXTEND_AGGREGATE_FIELD, AGGREGATE_FIELD.getTableColumnName(), "FLOWS") + " ; ";

        String bytesTimeSeries = "let BytesTimeSeries = " + getDirectionalBaseQuery(payload, tenantId, table, timeCondition.getCurrentTimeCondition()) +
                "| where " + DESTINATION_RESOURCE_ID.getTableColumnName() + " in(byBytes | project DestResId) " +
                transformGroupingToString(List.of(AGGREGATED_BYTES), metadata.getGroupByFields(), Optional.of(COUNT.getFieldDisplayName())) +
                ", TimeSeries = bin("+Fields.START_TIME.getTableColumnName()+", 1h)" +
                makeTimeSeriesCustom(COUNT.getFieldDisplayName(), derivedMetadata.getUpdatedCurrentTimeFrame(), 0, "DestResId, DestResourceName, DestResourceCategory, DestResourceType") +
                String.format(EXTEND_AGGREGATE_FIELD, AGGREGATE_FIELD.getTableColumnName(), "BYTES") + ";";

        return topDestResByBytes +
                topDestResByFlows +
                flowsTimeSeries +
                bytesTimeSeries +
                String.format(UNION_QUERY, "FlowsTimeSeries", "BytesTimeSeries");
    }

    private String getTopSourceTransferQuery(RequestContext requestContext, TimeCondition timeCondition, Metadata metadata) {
        RequestPayload payload = requestContext.getRequestPayload().get();
        String tenantId = requestContext.getTenantId().get();
        DerivedMetadata derivedMetadata= requestContext.getDerivedMetadata().get();

        List<Filters> filters = payload.getFilters();

        // For all line charts, the aggregate window is set to 1 hour. Because of this, the time window needs to be updated to read from Hourly Table always
        timeCondition.setCurrentTimeCondition("| where todatetime(StartTime) >= datetime('" + derivedMetadata.getUpdatedCurrentTimeFrame().getStartTime() +
                "') | where todatetime(EndTime) <= datetime('" + derivedMetadata.getUpdatedCurrentTimeFrame().getEndTime() + "')");
        timeCondition.setComparisonTimeCondition("| where todatetime(StartTime) >= datetime('" + derivedMetadata.getUpdatedComparisonTimeFrame().getStartTime() +
                "') | where todatetime(EndTime) <= datetime('" + derivedMetadata.getUpdatedComparisonTimeFrame().getEndTime() + "')");
        String table = "Insights_" + metadata.getTableName() + "_Hourly";

        String topSrcRes = getExternalTransferGenericSelectionQuery(
               timeCondition.getCurrentTimeCondition(),
                table,
                tenantId,
                "topSrcResTemp",
                new String[]{SOURCE_RESOURCE_ID.getTableColumnName(), SOURCE_RESOURCE_NAME.getTableColumnName(), SOURCE_RESOURCE_CATEGORY.getTableColumnName(), SOURCE_RESOURCE_TYPE.getTableColumnName(), AGGREGATED_FLOWS.getTableColumnName(), AGGREGATED_BYTES.getTableColumnName()},
                filters
        );
        String Flows = getExternalTransferGenericCurrAggregationQuery(
                List.of(AGGREGATED_FLOWS),
                List.of(SOURCE_RESOURCE_ID, SOURCE_RESOURCE_NAME, SOURCE_RESOURCE_CATEGORY, SOURCE_RESOURCE_TYPE),
                "topSrcRes",
                "Flows",
                payload.getPagination().getRowLimit(),
                "TotalFlowCount"
        );
        String Bytes = getExternalTransferGenericCurrAggregationQuery(
                List.of(AGGREGATED_BYTES),
                List.of(SOURCE_RESOURCE_ID, SOURCE_RESOURCE_NAME, SOURCE_RESOURCE_CATEGORY, SOURCE_RESOURCE_TYPE),
                "topSrcRes",
                "Bytes",
                payload.getPagination().getRowLimit(),
                "TotalByteCount"
        );
        String timeSeriesdata = getExternalTransferGenericSelectionQuery(
                timeCondition.getCurrentTimeCondition(),
                table,
                tenantId,
                "TimeSeriesDataTemp",
                new String[]{SOURCE_RESOURCE_ID.getTableColumnName(), SOURCE_RESOURCE_NAME.getTableColumnName(), SOURCE_RESOURCE_CATEGORY.getTableColumnName(), SOURCE_RESOURCE_TYPE.getTableColumnName(), AGGREGATED_FLOWS.getTableColumnName(), AGGREGATED_BYTES.getTableColumnName(), START_TIME.getTableColumnName()},
                filters
        );
        String flowsTimeSeries = getExternalTransferTimeSeriesQuery(
                "FlowsTimeSeries",
                "TimeSeriesData",
                SOURCE_RESOURCE_ID.getTableColumnName(),
                "Flows",
                List.of(AGGREGATED_FLOWS),
                List.of(SOURCE_RESOURCE_ID, SOURCE_RESOURCE_NAME, SOURCE_RESOURCE_CATEGORY, SOURCE_RESOURCE_TYPE),
                derivedMetadata.getUpdatedCurrentTimeFrame(),
                "FLOWS"
        );
        String bytesTimeSeries = getExternalTransferTimeSeriesQuery(
                "BytesTimeSeries",
                "TimeSeriesData",
                SOURCE_RESOURCE_ID.getTableColumnName(),
                "Bytes",
                List.of(AGGREGATED_BYTES),
                List.of(SOURCE_RESOURCE_ID, SOURCE_RESOURCE_NAME, SOURCE_RESOURCE_CATEGORY, SOURCE_RESOURCE_TYPE),
                derivedMetadata.getUpdatedCurrentTimeFrame(),
                "BYTES"
        );

        return topSrcRes+
                " let topSrcRes = topSrcResTemp | where isnotempty(SrcResId);" +
                Flows+
                Bytes+
                timeSeriesdata+
                " let TimeSeriesData = TimeSeriesDataTemp | where isnotempty(SrcResId);" +
                flowsTimeSeries+
                bytesTimeSeries+
                String.format(UNION_QUERY,"FlowsTimeSeries", "BytesTimeSeries");
    }

    /*
     4. Queries for Shadow LLM
    * */

    private String getLLMInUse(RequestContext requestContext, TimeCondition timeCondition, Metadata metadata) {
        RequestPayload payload = requestContext.getRequestPayload().orElseThrow();
        String tenantId = requestContext.getTenantId().orElseThrow();
        DerivedMetadata derivedMetadata = requestContext.getDerivedMetadata().orElseThrow();

        String currTable = derivedMetadata.getCurrentTimeFrameTable();
        String prevTable = derivedMetadata.getComparedTimeFrameTable();

        String CurrTimeFrame = timeCondition.getCurrentTimeCondition();
        String PrevTimeFrame = timeCondition.getComparisonTimeCondition();
        String query =
                        // current timeframe
                        "let llmsInUseCurr = " + currTable + " " +
                        String.format(TENANT_ID, tenantId) + " " +
                        CurrTimeFrame + " " +
                        transformFiltersToString(payload.getFilters()) +
                        "| where " + DESTINATION_EXTERNAL_LABEL.getTableColumnName() + " != 'Unknown Internet' " +
                        "| where " + Fields.DESTINATION_EXTERNAL_LABEL_CATEGORY.getTableColumnName() + " == \"LLM\" " +
                        "| summarize " +
                        Fields.AGGREGATED_FLOWS.getTableColumnName() + " = sum(AggFlowCount), " +
                        Fields.AGGREGATED_BYTES.getTableColumnName() + " = sum(AggByteCount) by " +
                        Fields.LLM.getTableColumnName() + "; " +

                        // previous timeframe
                        "let llmsInUsePrev = " + prevTable + " " +
                        String.format(TENANT_ID, tenantId) + " " +
                        PrevTimeFrame + " " +
                        transformFiltersToString(payload.getFilters()) +
                        "| where " + DESTINATION_EXTERNAL_LABEL.getTableColumnName() + " != 'Unknown Internet' " +
                        "| where " + Fields.DESTINATION_EXTERNAL_LABEL_CATEGORY.getTableColumnName() + " == \"LLM\" " +
                        "| summarize " +
                        Fields.PREVIOUS_AGGREGATED_FLOWS.getTableColumnName() + " = sum(AggFlowCount), " +
                        Fields.PREVIOUS_AGGREGATED_BYTES.getTableColumnName() + " = sum(AggByteCount) by " +
                        Fields.LLM.getTableColumnName() + "; " +

                        // join and final projection
                        "let rows = llmsInUseCurr " +
                        "| join kind=leftouter (llmsInUsePrev) on " + Fields.LLM.getTableColumnName() +
                        transformSortingToString(payload.getSortByFields()) +
                        tranformPaginationtoString(payload.getPagination());
        return query;
    }

    private String getTopCategoryWithLlm(RequestContext requestContext, TimeCondition timeCondition, Metadata metadata) {
        String orderBy = " | order by Count desc";
        RequestPayload payload = requestContext.getRequestPayload().orElseThrow();
        String tenantId = requestContext.getTenantId().orElseThrow();
        DerivedMetadata derivedMetadata = requestContext.getDerivedMetadata().orElseThrow();

        String direction = "OUTBOUND";

        String category = payload.getFilters() == null ? null : payload.getFilters().stream()
                .filter(f -> "category".equalsIgnoreCase(f.getCategoryName()))
                .flatMap(f -> f.getCategoryValue().stream())
                .map(Object::toString)
                .findFirst()
                .map(String::toUpperCase)
                .orElse(null);


        String categoryField = Fields.getDirectionBoundFields().get(direction).get(category).getTableColumnName();
        String labelField = Fields.DESTINATION_EXTERNAL_LABEL.getTableColumnName();
        Fields labelCategoryField = Fields.DESTINATION_EXTERNAL_LABEL_CATEGORY;
        String label = "LLM";
        String labelFilter = "| where " + labelCategoryField.getTableColumnName() + " == \"" + label + "\" ";

        // Flows setup
        String flowsQuery = getTopCategoryWithLlmAgg(derivedMetadata.getCurrentTimeFrameTable(), derivedMetadata.getComparedTimeFrameTable(), tenantId, payload, AGGREGATED_FLOWS, categoryField, labelField, labelFilter, "FLOWS", orderBy, timeCondition);

        // Bytes setup
        String bytesQuery = getTopCategoryWithLlmAgg(derivedMetadata.getCurrentTimeFrameTable(), derivedMetadata.getComparedTimeFrameTable(), tenantId, payload, AGGREGATED_BYTES, categoryField, labelField, labelFilter, "BYTES", orderBy, timeCondition);

        return flowsQuery + bytesQuery + " union summaryJoinFLOWS, summaryJoinBYTES, flowsResult, bytesResult ";
    }

    private String getTopCategoryWithLlmAgg(String currTable, String prevTable, String tenantId, RequestPayload payload, Fields aggField, String categoryField, String labelField, String labelFilter, String labelName, String orderBy, TimeCondition timeCondition) {
        String topCategory = "topCategory_" + labelName;
        String topLlm = "topLlm_" + labelName;
        String prevLlm = "prevLlm_" + labelName;
        String finalJoin = labelName.toLowerCase() + "Result";
        String summaryCurr = "summaryCurr" + labelName;
        String summaryPrev = "summaryPrev" + labelName;
        String summaryJoin = "summaryJoin" + labelName;

        String CurrTimeFrame = timeCondition.getCurrentTimeCondition();
        String PrevTimeFrame = timeCondition.getComparisonTimeCondition();

        String topCategoryQuery = "let " + topCategory + " = " + currTable +
                String.format(TENANT_ID, tenantId) +
                CurrTimeFrame +
                labelFilter +
                transformFiltersToString(payload.getFilters()) +
                "| summarize Count = sum(" + aggField.getTableColumnName() + ") by " + categoryField +
                orderBy +
                String.format(QUERY_DATA_LIMIT, payload.getPagination() == null ? TOP_QUERIES_LIMIT : payload.getPagination().getRowLimit()) + " ; ";

        String topLlmQuery = "let " + topLlm + " = " + currTable +
                String.format(TENANT_ID, tenantId) +
                CurrTimeFrame +
                labelFilter +
                transformFiltersToString(payload.getFilters()) +
                "| where " + categoryField + " in (" + topCategory + ") " +
                "| summarize Count = sum(" + aggField.getTableColumnName() + "), CategoryId = take_any(" + categoryField + ", isnotempty(" + categoryField + ")), " + "AccountName = take_anyif(SrcAccountName, isnotempty(SrcAccountName)), " + "CloudProvider = take_anyif(SourceZone, isnotempty(SourceZone)) " +
                "by " + categoryField + ", " + labelField +
                orderBy +
                String.format(QUERY_DATA_LIMIT, payload.getPagination() == null ? TOP_QUERIES_LIMIT : payload.getPagination().getRowLimit()) + " ; ";

        String prevLlmQuery = "let " + prevLlm + " = " + prevTable +
                String.format(TENANT_ID, tenantId) +
                PrevTimeFrame +
                labelFilter +
                transformFiltersToString(payload.getFilters()) +
                "| where " + categoryField + " in (" + topCategory + ") " +
                "| summarize Count = sum(" + aggField.getTableColumnName() + "), CategoryId = take_any(" + categoryField + ", isnotempty(" + categoryField + ")), " + "AccountName = take_anyif(SrcAccountName, isnotempty(SrcAccountName)), " + "CloudProvider = take_anyif(SourceZone, isnotempty(SourceZone)) " +
                "by " + categoryField + ", " + labelField +
                orderBy +
                String.format(QUERY_DATA_LIMIT, payload.getPagination() == null ? TOP_QUERIES_LIMIT : payload.getPagination().getRowLimit()) + " ; ";

        String finalProjection = "let " + finalJoin + " = " + topLlm +
                "| join kind=leftouter (" + prevLlm + ") on " + categoryField + ", " + labelField +
                "| extend " +
                "DestinationExternalLabel = " + labelField + ", " + "AggregateField = \"" + labelName + "\"; ";

        String summaryCurrQuery = "let " + summaryCurr + " = " + topLlm +
                "| summarize Count = sum(Count), " +
                "CategoryId = take_any(CategoryId), " +
                "CloudProvider = take_any(CloudProvider) " +
                "by AccountName, " + categoryField + "; ";

        String summaryPrevQuery = "let " + summaryPrev + " = " + prevLlm +
                "| summarize Count = sum(Count)," +
                "CategoryId = take_any(CategoryId), " +
                "CloudProvider = take_any(CloudProvider) " +
                "by AccountName, " + categoryField + "; ";

        String summaryJoinQuery = "let " + summaryJoin + " = " + summaryCurr +
                "| join kind=leftouter (" + summaryPrev + ") on AccountName, " + categoryField +
                "| extend " +
                "DestinationExternalLabel = '*', " + "AggregateField = \"" + labelName + "\"; ";

        return topCategoryQuery + topLlmQuery + prevLlmQuery + finalProjection + summaryCurrQuery + summaryPrevQuery + summaryJoinQuery;
    }


    private String getTopSourcesWithLlm(RequestContext requestContext, TimeCondition timeCondition, Metadata metadata) {

        RequestPayload payload = requestContext.getRequestPayload().orElseThrow();
        String tenantId = requestContext.getTenantId().orElseThrow();
        DerivedMetadata derivedMetadata = requestContext.getDerivedMetadata().orElseThrow();

        // For all line charts, the aggregate window is set to 1 hour. Because of this, the time window needs to be updated to read from Hourly Table always
        timeCondition.setCurrentTimeCondition("| where todatetime(StartTime) >= datetime('" + derivedMetadata.getUpdatedCurrentTimeFrame().getStartTime() +
                "') | where todatetime(EndTime) <= datetime('" + derivedMetadata.getUpdatedCurrentTimeFrame().getEndTime() + "')");
        timeCondition.setComparisonTimeCondition("| where todatetime(StartTime) >= datetime('" + derivedMetadata.getUpdatedComparisonTimeFrame().getStartTime() +
                "') | where todatetime(EndTime) <= datetime('" + derivedMetadata.getUpdatedComparisonTimeFrame().getEndTime() + "')");
        String table = "Insights_" + metadata.getTableName() + "_Hourly";

        String currTimeFrame = timeCondition.getCurrentTimeCondition();

        String labelCategoryField = Fields.DESTINATION_EXTERNAL_LABEL_CATEGORY.getTableColumnName();

        // sort IPs by FlowCount
        String topSourcesflows = "let topSourcesByFlows = " + table + " " +
                String.format(TENANT_ID, tenantId) + " " +
                currTimeFrame + " " +
                "| where " + labelCategoryField + " == \"LLM\" " +
                transformFiltersToString(payload.getFilters()) +
                "| summarize Count = sum(" + AGGREGATED_FLOWS.getTableColumnName() + ") by SrcResId, SrcResourceName, SrcResourceCategory, SrcResourceType" +
                String.format(QUERY_DATA_LIMIT, payload.getPagination() == null ? TOP_QUERIES_LIMIT : payload.getPagination().getRowLimit()) + " ; ";

        String topSourcesbytes = "let topSourcesByBytes = " + table + " " +
                String.format(TENANT_ID, tenantId) + " " +
                currTimeFrame + " " +
                "| where " + labelCategoryField + " == \"LLM\" " +
                transformFiltersToString(payload.getFilters()) +
                "| summarize Count = sum(" + AGGREGATED_BYTES.getTableColumnName() + ") by SrcResId, SrcResourceName, SrcResourceCategory, SrcResourceType" +
                String.format(QUERY_DATA_LIMIT, payload.getPagination() == null ? TOP_QUERIES_LIMIT : payload.getPagination().getRowLimit()) + " ; ";

        // flow time series
        String makeSeriesFlows = "let flowSeries = " + table + " " +
                String.format(TENANT_ID, tenantId) + " " +
                currTimeFrame + " " +
                "| where " + labelCategoryField + " == \"LLM\" " +
                transformFiltersToString(payload.getFilters()) +
                "| where SrcResId in ( topSourcesByFlows | project SrcResId ) " +
                "| summarize Count = sum(" + AGGREGATED_FLOWS.getTableColumnName() + ") by SrcResId, SrcResourceName, SrcResourceCategory, SrcResourceType" +
                ", TimeSeries = bin("+Fields.START_TIME.getTableColumnName()+", 1h)" +
                makeTimeSeriesCustom(COUNT.getFieldDisplayName(), derivedMetadata.getUpdatedCurrentTimeFrame(), 0, "SrcResId, SrcResourceName, SrcResourceCategory, SrcResourceType") +
                String.format(EXTEND_AGGREGATE_FIELD, AGGREGATE_FIELD.getTableColumnName(), "FLOWS") + " ; " ;

        // byte time series
        String makeSeriesBytes = "let byteSeries = " + table + " " +
                String.format(TENANT_ID, tenantId) + " " +
                currTimeFrame + " " +
                "| where " + labelCategoryField + " == \"LLM\" " +
                transformFiltersToString(payload.getFilters()) +
                "| where SrcResId in ( topSourcesByBytes | project SrcResId ) " +
                "| summarize Count = sum(" + AGGREGATED_BYTES.getTableColumnName() + ") by SrcResId, SrcResourceName, SrcResourceCategory, SrcResourceType" +
                ", TimeSeries = bin("+Fields.START_TIME.getTableColumnName()+", 1h)" +
                makeTimeSeriesCustom(COUNT.getFieldDisplayName(), derivedMetadata.getUpdatedCurrentTimeFrame(), 0, "SrcResId, SrcResourceName, SrcResourceCategory, SrcResourceType") +
                String.format(EXTEND_AGGREGATE_FIELD, AGGREGATE_FIELD.getTableColumnName(), "BYTES") + " ; ";
        // union both
        String unionResult = "union flowSeries, byteSeries; ";

        return topSourcesflows + topSourcesbytes + makeSeriesFlows + makeSeriesBytes + unionResult;
    }


    /*
     5. Queries for DORA Compliance Page
     */

    private String getThirdPartyDependencyInbound(RequestContext requestContext, TimeCondition timeCondition, Metadata metadata) {
        RequestPayload payload = requestContext.getRequestPayload().get();
        String tenantId = requestContext.getTenantId().get();
        DerivedMetadata derivedMetadata = requestContext.getDerivedMetadata().get();

        return getBaseQueryForTimeFrame(payload, metadata, tenantId, timeCondition.getCurrentTimeCondition(), "curr", derivedMetadata.getCurrentTimeFrameTable()) +
                getBaseQueryForTimeFrame(payload, metadata, tenantId, timeCondition.getComparisonTimeCondition(), "prev", derivedMetadata.getComparedTimeFrameTable()) +
                "let rows = " +
                " curr | join kind = leftouter(prev) on " + getGroupByFields(metadata.getGroupByFields()) +
                transformSortingToString(payload.getSortByFields()) +
                tranformPaginationtoString(payload.getPagination());
    }

    private String getThirdPartyDependencyOutbound(RequestContext requestContext, TimeCondition timeCondition, Metadata metadata){
        RequestPayload payload = requestContext.getRequestPayload().get();
        String tenantId = requestContext.getTenantId().get();
        DerivedMetadata derivedMetadata = requestContext.getDerivedMetadata().get();

        return getBaseQueryForTimeFrame(payload, metadata, tenantId, timeCondition.getCurrentTimeCondition(), "curr", derivedMetadata.getCurrentTimeFrameTable()) +
                getBaseQueryForTimeFrame(payload, metadata, tenantId, timeCondition.getComparisonTimeCondition(), "prev", derivedMetadata.getComparedTimeFrameTable()) +
                "let rows = " +
                " curr | join kind = leftouter(prev) on " + getGroupByFields(metadata.getGroupByFields()) +
                transformSortingToString(payload.getSortByFields()) +
                tranformPaginationtoString(payload.getPagination());
    }

    private String getUnencryptedServices(RequestContext requestContext, TimeCondition timeCondition, Metadata metadata){
        RequestPayload payload = requestContext.getRequestPayload().get();
        String tenantId = requestContext.getTenantId().get();
        DerivedMetadata derivedMetadata = requestContext.getDerivedMetadata().get();
        Map<String, String> optionalFilters = new HashMap<>();
        optionalFilters.put("IsUnencrypted", "true");

        return getBaseQueryForTimeFrame(payload, metadata, tenantId, timeCondition.getCurrentTimeCondition(), "curr", derivedMetadata.getCurrentTimeFrameTable(), optionalFilters) +
                getBaseQueryForTimeFrame(payload, metadata, tenantId, timeCondition.getComparisonTimeCondition(), "prev", derivedMetadata.getComparedTimeFrameTable(), optionalFilters) +
                "let rows = " +
                " curr | join kind = leftouter(prev) on " + getGroupByFields(metadata.getGroupByFields()) +
                transformSortingToString(payload.getSortByFields()) +
                tranformPaginationtoString(payload.getPagination());
    }

    private String getDoraSelectionQuery(String timeCondition, String tableName, String tenantId, String label, List<Filters> filters) {

        return "let "+label+" = "+tableName+
                timeCondition +
                String.format(TENANT_ID, tenantId) +
                transformFiltersToString(filters) + ";";
    }

    private String getDoraCurrData(String baseTable, String labelName, String direction, String aggField, String aggFieldLabel, String category, RequestPayload payload) {
        String threatField = Fields.getDirectionBoundFields().get(direction).get("THREATLEVEL").getTableColumnName();
        String accountName = Fields.getDirectionBoundFields().get(direction).get("ACCOUNTNAME").getTableColumnName();
        String categoryId = Fields.getDirectionBoundFields().get(direction).get(category).getTableColumnName();
        String cloudprovider = Fields.getDirectionBoundFields().get(direction).get("CLOUDPROVIDER").getTableColumnName();

        return "let " + labelName + " = " + baseTable +
                "|where isnotempty(" + categoryId + ")" +
                "|where "+threatField+">=2"+
                "|summarize "+aggFieldLabel+" =sum("+aggField+"), AccountName=take_anyif("+accountName+", isnotempty("+accountName+")),"+
                "CloudProvider=take_anyif("+cloudprovider+", isnotempty("+cloudprovider+")) by "+CATEGORY_ID.getTableColumnName()+"="+categoryId+
                "|order by "+aggFieldLabel+" desc "+
                String.format(QUERY_DATA_LIMIT, payload.getPagination() == null ? TOP_QUERIES_LIMIT : payload.getPagination().getRowLimit()) + " ; ";
    }

    private String getDoraPrevData(String baseTable, String labelName, String currTableName, String direction, String aggField, String aggFieldLabel, String category) {
        String threatField = Fields.getDirectionBoundFields().get(direction).get("THREATLEVEL").getTableColumnName();
        String categoryId = Fields.getDirectionBoundFields().get(direction).get(category).getTableColumnName();

        return "let " + labelName + " = " + baseTable +
                "|where isnotempty(" + categoryId + ") " +
                "|where "+threatField+">=2"+
                "|where "+categoryId+" in ("+currTableName+") "+
                "|summarize "+aggFieldLabel+" =sum("+aggField+") by "+CATEGORY_ID.getTableColumnName()+"="+categoryId+";";
    }

    private String getDoraJoinQuery(String lhsTable, String rhsTable, String labelName, String joinKey) {
        return "let " + labelName + " = " + lhsTable +
                "|join kind = leftouter(" + rhsTable + ") on " + joinKey+";";
    }

    private String getDoraUnionQuery(String lhsTable, String rhsTable, String labelName, String aggField, String prevAggField, String aggFieldLabel, String rowLimit) {
        return "let " + labelName + " = union " + lhsTable + "," +rhsTable+
                "|top "+rowLimit+" by "+aggField+
                "|extend AggregateField=\""+aggFieldLabel+"\", "+COUNT.getTableColumnName()+"="+aggField+", "+PREVIOUS_COUNT.getTableColumnName()+"="+prevAggField+";";
    }

    private String getDoraTopIct(RequestContext requestContext, TimeCondition timeCondition) {
        RequestPayload payload = requestContext.getRequestPayload().get();
        String tenantId = requestContext.getTenantId().get();
        DerivedMetadata derivedMetadata = requestContext.getDerivedMetadata().get();
        String category = payload.getFilters() == null ? null : payload.getFilters().stream()
                .filter(f -> "category".equalsIgnoreCase(f.getCategoryName()))
                .flatMap(f -> f.getCategoryValue().stream())
                .filter(val -> val instanceof String)
                .map(val -> ((String) val).toUpperCase())
                .findFirst()
                .orElse(null);

        List<Filters> filters = payload.getFilters();

        String curr = getDoraSelectionQuery(
                timeCondition.getCurrentTimeCondition(),
                derivedMetadata.getCurrentTimeFrameTable(),
                tenantId,
                "curr",
                filters
        );
        String prev = getDoraSelectionQuery(
                timeCondition.getComparisonTimeCondition(),
                derivedMetadata.getComparedTimeFrameTable(),
                tenantId,
                "prev",
                filters
        );
        String inboundCurrFlows = getDoraCurrData(
                "curr",
                "inboundCurrFlows",
                "INBOUND",
                AGGREGATED_FLOWS.getTableColumnName(),
                "TotalFlowCount",
                category,
                payload
        );
        String inboundCurrBytes = getDoraCurrData(
                "curr",
                "inboundCurrBytes",
                "INBOUND",
                AGGREGATED_BYTES.getTableColumnName(),
                "TotalByteCount",
                category,
                payload
        );
        String inboundPrevFlows = getDoraPrevData(
                "prev",
                "inboundPrevFlows",
                "inboundCurrFlows",
                "INBOUND",
                AGGREGATED_FLOWS.getTableColumnName(),
                "TotalFlowCountPrev",
                category
        );
        String inboundPrevBytes = getDoraPrevData(
                "prev",
                "inboundPrevBytes",
                "inboundCurrBytes",
                "INBOUND",
                AGGREGATED_BYTES.getTableColumnName(),
                "TotalByteCountPrev",
                category
        );
        String outboundCurrFlows = getDoraCurrData(
                "curr",
                "outboundCurrFlows",
                "OUTBOUND",
                AGGREGATED_FLOWS.getTableColumnName(),
                "TotalFlowCount",
                category,
                payload
        );
        String outboundCurrBytes = getDoraCurrData(
                "curr",
                "outboundCurrBytes",
                "OUTBOUND",
                AGGREGATED_BYTES.getTableColumnName(),
                "TotalByteCount",
                category,
                payload
        );
        String outboundPrevFlows = getDoraPrevData(
                "prev",
                "outboundPrevFlows",
                "outboundCurrFlows",
                "OUTBOUND",
                AGGREGATED_FLOWS.getTableColumnName(),
                "TotalFlowCountPrev",
                category
        );
        String outboundPrevBytes = getDoraPrevData(
                "prev",
                "outboundPrevBytes",
                "outboundCurrBytes",
                "OUTBOUND",
                AGGREGATED_BYTES.getTableColumnName(),
                "TotalByteCountPrev",
                category
        );
        String inboundFlows = getDoraJoinQuery(
                "inboundCurrFlows",
                "inboundPrevFlows",
                "inboundFlows",
                CATEGORY_ID.getTableColumnName()
        );
        String inboundBytes = getDoraJoinQuery(
                "inboundCurrBytes",
                "inboundPrevBytes",
                "inboundBytes",
                CATEGORY_ID.getTableColumnName()
        );
        String outboundFlows = getDoraJoinQuery(
                "outboundCurrFlows",
                "outboundPrevFlows",
                "outboundFlows",
                CATEGORY_ID.getTableColumnName()
        );
        String outboundBytes = getDoraJoinQuery(
                "outboundCurrBytes",
                "outboundPrevBytes",
                "outboundBytes",
                CATEGORY_ID.getTableColumnName()
        );
        String flows = getDoraUnionQuery(
                "inboundFlows",
                "outboundFlows",
                "flows",
                "TotalFlowCount",
                "TotalFlowCountPrev",
                "FLOWS",
                String.valueOf(payload.getPagination() == null ? TOP_QUERIES_LIMIT : payload.getPagination().getRowLimit())

        );
        String bytes = getDoraUnionQuery(
                "inboundBytes",
                "outboundBytes",
                "bytes",
                "TotalByteCount",
                "TotalByteCountPrev",
                "BYTES",
                String.valueOf(payload.getPagination() == null ? TOP_QUERIES_LIMIT : payload.getPagination().getRowLimit())

        );

        // Combine and union the flows and bytes results
        return curr +
                prev +
                inboundCurrFlows +
                inboundCurrBytes +
                inboundPrevFlows +
                inboundPrevBytes +
                outboundCurrFlows +
                outboundCurrBytes +
                outboundPrevFlows +
                outboundPrevBytes +
                inboundFlows +
                inboundBytes +
                outboundFlows +
                outboundBytes +
                flows +
                bytes +
                "union flows,bytes";
    }

    private String getCriticalIctSelection(String label, String baseTable, String groupByField, String aggField, String aggFieldLabel){
        return "let "+label+" = "+baseTable+
                "|where isnotempty("+groupByField+")"+
                "|summarize "+aggFieldLabel+"=sum("+aggField+") by "+groupByField+";";
    }

    private String getCriticalIctJoin(String lhsTable, String rhsTable, String label, String srcAggField, String destAggField, String aggFieldLabel){
        return "let "+label+" = "+lhsTable+
                "|join kind=fullouter "+rhsTable+" on $left."+SOURCE_ROLE.getTableColumnName()+" == $right."+DESTINATION_ROLE.getTableColumnName()+
                "|extend "+ROLE.getTableColumnName()+" = coalesce("+SOURCE_ROLE.getTableColumnName()+","+DESTINATION_ROLE.getTableColumnName()+"), "+aggFieldLabel+"= coalesce("+srcAggField+", 0) + coalesce("+destAggField+", 0)";
    }

    private String getCriticalIct(String label, String lhsTable, String rhsTable, String joinKey, String orderBy, String aggField){
        return "let "+label+" = "+lhsTable+
                "|join kind=leftouter "+rhsTable+" on "+joinKey+
                "|extend AggregateField=\""+aggField+"\""+
                "|order by "+orderBy+" desc;";
    }

    private String getCriticalIctQuery(RequestContext requestContext, TimeCondition timeCondition) {
        RequestPayload payload = requestContext.getRequestPayload().get();
        String tenantId = requestContext.getTenantId().get();
        DerivedMetadata derivedMetadata = requestContext.getDerivedMetadata().get();
        List<Filters> filters = payload.getFilters();

        String curr = getDoraSelectionQuery(timeCondition.getCurrentTimeCondition(), derivedMetadata.getCurrentTimeFrameTable(), tenantId, "curr", filters);
        String prev = getDoraSelectionQuery(timeCondition.getComparisonTimeCondition(), derivedMetadata.getComparedTimeFrameTable(), tenantId, "prev", filters);
        String riskySrcRolesCurrFlows = getCriticalIctSelection("riskySrcRolesCurrFlows", "curr", SOURCE_ROLE.getTableColumnName(), AGGREGATED_FLOWS.getTableColumnName(), "SrcFlowCount");
        String riskySrcRolesCurrBytes = getCriticalIctSelection("riskySrcRolesCurrBytes", "curr", SOURCE_ROLE.getTableColumnName(), AGGREGATED_BYTES.getTableColumnName(), "SrcByteCount");
        String riskyDestRolesCurrFlows = getCriticalIctSelection("riskyDestRolesCurrFlows", "curr", DESTINATION_ROLE.getTableColumnName(), AGGREGATED_FLOWS.getTableColumnName(), "DestFlowCount");
        String riskyDestRolesCurrBytes = getCriticalIctSelection("riskyDestRolesCurrBytes", "curr", DESTINATION_ROLE.getTableColumnName(), AGGREGATED_BYTES.getTableColumnName(), "DestByteCount");
        String riskySrcRolesPrevFlows = getCriticalIctSelection("riskySrcRolesPrevFlows", "prev", SOURCE_ROLE.getTableColumnName(), AGGREGATED_FLOWS.getTableColumnName(), "SrcFlowCount");
        String riskySrcRolesPrevBytes = getCriticalIctSelection("riskySrcRolesPrevBytes", "prev", SOURCE_ROLE.getTableColumnName(), AGGREGATED_BYTES.getTableColumnName(), "SrcByteCount");
        String riskyDestRolesPrevFlows = getCriticalIctSelection("riskyDestRolesPrevFlows", "prev", DESTINATION_ROLE.getTableColumnName(), AGGREGATED_FLOWS.getTableColumnName(), "DestFlowCount");
        String riskyDestRolesPrevBytes = getCriticalIctSelection("riskyDestRolesPrevBytes", "prev", DESTINATION_ROLE.getTableColumnName(), AGGREGATED_BYTES.getTableColumnName(), "DestByteCount");
        String riskyRolesCurrFlows = getCriticalIctJoin("riskySrcRolesCurrFlows", "riskyDestRolesCurrFlows", "riskyRolesCurrFlows", "SrcFlowCount", "DestFlowCount", COUNT.getTableColumnName());
        String riskyRolesCurrBytes = getCriticalIctJoin("riskySrcRolesCurrBytes", "riskyDestRolesCurrBytes", "riskyRolesCurrBytes", "SrcByteCount", "DestByteCount", COUNT.getTableColumnName());
        String riskyRolesPrevFlows = getCriticalIctJoin("riskySrcRolesPrevFlows", "riskyDestRolesPrevFlows", "riskyRolesPrevFlows", "SrcFlowCount", "DestFlowCount", COUNT.getTableColumnName());
        String riskyRolesPrevBytes = getCriticalIctJoin("riskySrcRolesPrevBytes", "riskyDestRolesPrevBytes", "riskyRolesPrevBytes", "SrcByteCount", "DestByteCount", COUNT.getTableColumnName());
        String flows = getCriticalIct("flows", "riskyRolesCurrFlows", "riskyRolesPrevFlows", ROLE.getTableColumnName(), COUNT.getTableColumnName(), "FLOWS");
        String bytes = getCriticalIct("bytes", "riskyRolesCurrBytes", "riskyRolesPrevBytes", ROLE.getTableColumnName(), COUNT.getTableColumnName(), "BYTES");

        return curr +
                prev+
                riskySrcRolesCurrFlows+
                riskySrcRolesCurrBytes+
                riskyDestRolesCurrFlows+
                riskyDestRolesCurrBytes+
                riskySrcRolesPrevFlows+
                riskySrcRolesPrevBytes+
                riskyDestRolesPrevFlows+
                riskyDestRolesPrevBytes+
                riskyRolesCurrFlows+"| top "+(payload.getPagination() == null ? TOP_QUERIES_LIMIT : payload.getPagination().getRowLimit())+" by "+COUNT.getTableColumnName()+";"+
                riskyRolesCurrBytes+"| top "+(payload.getPagination() == null ? TOP_QUERIES_LIMIT : payload.getPagination().getRowLimit())+" by "+COUNT.getTableColumnName()+";"+
                riskyRolesPrevFlows+";"+
                riskyRolesPrevBytes+";"+
                flows+
                bytes+
                "union flows, bytes";
    }

    // -----------------------------------
    // Cross Region Traffic Queries
    // -----------------------------------

    private String getCrossRegionTrafficQuery(RequestContext requestContext, TimeCondition timeCondition, Metadata metadata) {
        String optionalFilter = " | where SrcRegion != DestRegion " ;

        String flows = getCrossRegionBaseQuery(requestContext, metadata, timeCondition, AGGREGATED_FLOWS, "flows", optionalFilter);
        String bytes = getCrossRegionBaseQuery(requestContext, metadata, timeCondition, AGGREGATED_BYTES, "bytes", optionalFilter);
        String union = "union flows, bytes ;";

        return flows + bytes + union;
    }

    private String getRegionToCountryTrafficQuery(RequestContext requestContext, TimeCondition timeCondition, Metadata metadata) {
        String flows = getCrossRegionBaseQuery(requestContext, metadata, timeCondition, AGGREGATED_FLOWS, "flows", "");
        String bytes = getCrossRegionBaseQuery(requestContext, metadata, timeCondition, AGGREGATED_BYTES, "bytes", "");
        String union = "union flows, bytes ;";

        return flows + bytes + union;
    }

    private String getCrossRegionBaseQuery(RequestContext requestContext, Metadata metadata, TimeCondition timeCondition, Fields aggField, String extendField, String optionalFilter) {
        DerivedMetadata derivedMetadata= requestContext.getDerivedMetadata().get();
        RequestPayload payload = requestContext.getRequestPayload().get();

        return "let by" + aggField.getFieldKey() + " = "  +
                getBaseQuery(payload, requestContext.getTenantId().get(), derivedMetadata.getCurrentTimeFrameTable(), timeCondition.getCurrentTimeCondition()) +
                optionalFilter +
                transformGroupingToString(List.of(aggField), metadata.getGroupByFields(), Optional.of("Count")) +
                " | order by Count desc " +
                String.format(QUERY_DATA_LIMIT, payload.getPagination() == null ? TOP_QUERIES_LIMIT : payload.getPagination().getRowLimit()) +
                String.format(EXTEND_AGGREGATE_FIELD, AGGREGATE_FIELD.getTableColumnName(), extendField) + ";" +

                "let prevBy" + aggField.getFieldKey() + " = "  +
                getBaseQuery(payload, requestContext.getTenantId().get(), derivedMetadata.getCurrentTimeFrameTable(), timeCondition.getCurrentTimeCondition()) +
                optionalFilter +
                " |where " + SOURCE_REGION.getTableColumnName() + " in (by" + aggField.getFieldKey() + " | project " + SOURCE_REGION.getTableColumnName() + ")" +
                transformGroupingToString(List.of(aggField), metadata.getGroupByFields(), Optional.of("Count")) + ";" +

                " let " + extendField + " = by" + aggField.getFieldKey() + " | join kind = leftouter(prevBy" + aggField.getFieldKey() + ") on " + getGroupByFields(metadata.getGroupByFields()) + " ; ";
    }

    // -----------------------------------
    // Resource Insights Traffic
    // -----------------------------------

    private String getResourceBaseQuery(String name, String tableName, String tenantId, String CurrTimeFrame, String resourceId, String resource_direction, List<Filters> filters) {
        String query = "let " + name + " = " + tableName +
                        String.format(TENANT_ID, tenantId) +
                        CurrTimeFrame +
                        "| where " + resource_direction + " in~ " + resourceId;
        // checking if trafficStatus filter is present, if so add it to the query
        if (filters != null && filters.stream().anyMatch(f -> "traffic_status".equalsIgnoreCase(f.getCategoryName()))) {
            String trafficStatus = filters.stream()
                    .filter(f -> "traffic_status".equalsIgnoreCase(f.getCategoryName()))
                    .flatMap(f -> f.getCategoryValue().stream())
                    .filter(val -> val instanceof String)
                    .map(val -> ((String) val).toUpperCase())
                    .findFirst()
                    .orElse(null);
            query += "| where TrafficStatus in ('" + trafficStatus + "')";
        }
        return query;
    }

    private String getRiskyTrafficResourceInsightsQuery(RequestPayload payload, String currDirection, String prevDirection, String direction, String currTableName, String prevTableName, String tenantId, String CurrTimeFrame, String PrevTimeFrame, String resourceId, String resource_direction, Fields aggField, String labelName) {
        String resourceName = Fields.getDirectionBoundFields().get(resource_direction).get("RESOURCE_NAME").getTableColumnName();
        String resourceCategory = Fields.getDirectionBoundFields().get(resource_direction).get("RESOURCE_CATEGORY").getTableColumnName();
        String role = Fields.getDirectionBoundFields().get(resource_direction).get("ROLE").getTableColumnName();
        String directionId = Fields.getDirectionBoundFields().get(resource_direction).get("RESOURCEID").getTableColumnName();
        String trafficDirection = Fields.getDirectionBoundFields().get(resource_direction).get("DIRECTION").getTableColumnName();
        List<Filters> filters = payload.getFilters();
        String query =
                getResourceBaseQuery(currDirection, currTableName, tenantId, CurrTimeFrame, resourceId, directionId, filters) +
                "| summarize Count = sum(" + aggField.getTableColumnName() + "), ResourceName=take_anyif(" + resourceName + ", isnotempty(" + resourceName + ")), ResourceCategory=take_anyif(" + resourceCategory + ", isnotempty(" + resourceCategory + ")) by " + role + ";" +

                getResourceBaseQuery(prevDirection, prevTableName, tenantId, PrevTimeFrame, resourceId, directionId, filters) +
                "| where SourceLabel in (" + currDirection + ")" +
                "| summarize Count1 = sum(" + aggField.getTableColumnName() + ") by " + role + ";" +

                "let " + direction + " = " + currDirection +
                "| join kind=leftouter (" + prevDirection + ") on " + role +
                "| project ResourceCategory, ResourceName, Role = " + role + ", TrafficDirection = \"" + trafficDirection + "\", Count, Count1" +
                "| extend AggregateField = \""+ labelName + "\"" +
                String.format(QUERY_DATA_LIMIT, payload.getPagination() == null ? TOP_QUERIES_LIMIT : payload.getPagination().getRowLimit()) + " ; ";

        return query;
    }

    private String getRiskyTrafficBaseQuery(String currTable, String prevTable, String tenantId, RequestPayload payload, Fields aggField, String resourceId, String labelName, TimeCondition timeCondition) {
        String orderBy = " | order by Count asc";

        String sourceCurr = "sourceCurr" + labelName;
        String sourcePrev = "sourcePrev" + labelName;
        String source = "source" + labelName;
        String destinationCurr = "destinationCurr" + labelName;
        String destinationPrev = "destinationPrev" + labelName;
        String destination = "destination" + labelName;
        String result = labelName.toLowerCase();

        String CurrTimeFrame = timeCondition.getCurrentTimeCondition();
        String PrevTimeFrame = timeCondition.getComparisonTimeCondition();

        String query =
                        getRiskyTrafficResourceInsightsQuery(payload, sourceCurr, sourcePrev, source, currTable, prevTable, tenantId, CurrTimeFrame, PrevTimeFrame, resourceId, "INBOUND", aggField, labelName) +
                        getRiskyTrafficResourceInsightsQuery(payload, destinationCurr, destinationPrev, destination, currTable, prevTable, tenantId, CurrTimeFrame, PrevTimeFrame, resourceId, "OUTBOUND", aggField, labelName) +
                        "let " + result + " = union " +  source + ", " + destination + orderBy + ";";

        return query;

    }

    private String getRiskyTrafficByRolesResourceInsightsQuery(RequestContext requestContext, TimeCondition timeCondition) {
        RequestPayload payload = requestContext.getRequestPayload().orElseThrow();
        String tenantId = requestContext.getTenantId().orElseThrow();
        DerivedMetadata derivedMetadata = requestContext.getDerivedMetadata().orElseThrow();

        List<String> resourceIdList = payload.getFilters() == null ? null : payload.getFilters().stream()
                .filter(f -> "resource_id".equalsIgnoreCase(f.getCategoryName()))
                .flatMap(f -> f.getCategoryValue().stream())
                .map(Object::toString)
                .map(String::toLowerCase)
                .toList();

        String resourceId = "(" + resourceIdList.stream()
                .map(label -> "'" + label + "'")
                .collect(Collectors.joining(", ")) + ")";

        String flows = getRiskyTrafficBaseQuery(derivedMetadata.getCurrentTimeFrameTable(), derivedMetadata.getComparedTimeFrameTable(), tenantId, payload, AGGREGATED_FLOWS, resourceId, "FLOWS", timeCondition);
        String bytes = getRiskyTrafficBaseQuery(derivedMetadata.getCurrentTimeFrameTable(), derivedMetadata.getComparedTimeFrameTable(), tenantId, payload, AGGREGATED_BYTES, resourceId, "BYTES", timeCondition);
        String union = " union flows, bytes";

        return flows + bytes + union;
    }

    private String getMaliciousIpTrafficBaseQuery(RequestPayload payload, String tenantId, Fields aggField, String resourceId, String labelName, TimeCondition timeCondition, DerivedMetadata derivedMetadata, Metadata metadata) {

        String topMaliciousIpInbound = "topMaliciousIpInbound" + labelName;
        String topMaliciousIpOutbound = "topMaliciousIpOutbound" + labelName;
        String topMaliciousIp = "topMaliciousIp" + labelName;
        String result = labelName.toLowerCase();

        // For all line charts, the aggregate window is set to 1 hour. Because of this, the time window needs to be updated to read from Hourly Table always
        timeCondition.setCurrentTimeCondition("| where todatetime(StartTime) >= datetime('" + derivedMetadata.getUpdatedCurrentTimeFrame().getStartTime() +
                "') | where todatetime(EndTime) <= datetime('" + derivedMetadata.getUpdatedCurrentTimeFrame().getEndTime() + "')");
        timeCondition.setComparisonTimeCondition("| where todatetime(StartTime) >= datetime('" + derivedMetadata.getUpdatedComparisonTimeFrame().getStartTime() +
                "') | where todatetime(EndTime) <= datetime('" + derivedMetadata.getUpdatedComparisonTimeFrame().getEndTime() + "')");
        String table = "Insights_" + metadata.getTableName() + "_Hourly";

        String currTimeFrame = timeCondition.getCurrentTimeCondition();

        Optional<String> order = payload.getSortByFields().stream()
                .map(SortByFields::getOrder)
                .findFirst();

        List<Filters> filters = payload.getFilters();

        Integer count = payload.getPagination() == null ? TOP_QUERIES_LIMIT : payload.getPagination().getRowLimit();

        String sorting = order.map(o -> "| top " + count + " by Count " + o).orElse("");

        String query =
                        getResourceBaseQuery(topMaliciousIpInbound, table, tenantId, currTimeFrame, resourceId, "SrcResId", filters) +
                        "| where DestThreatLevel >= 2" +
                        "| summarize CountIn = sum(" + aggField.getTableColumnName() + ") by DestIP;" +
                        getResourceBaseQuery(topMaliciousIpOutbound, table, tenantId, currTimeFrame, resourceId, "DestResId", filters) +
                        "| where SrcThreatLevel >= 2" +
                        "| summarize CountOut = sum(" + aggField.getTableColumnName() + ") by SrcIP;" +

                        "let " + topMaliciousIp + " = union (" + topMaliciousIpInbound + " | project Ip=DestIP, Count=CountIn)," +
                        "(" + topMaliciousIpOutbound + " | project Ip=SrcIP, Count=CountOut)" +
                        "| summarize Count = sum(Count) by Ip" +
                        sorting +
                        "| project Ip;" +

                        "let " + result + " = " + table +
                        String.format(TENANT_ID, tenantId) +
                        "| extend Ip = case(SrcResId in~ " + resourceId + " and DestThreatLevel >= 2, DestIP," +
                        "DestResId in~ " + resourceId + " and SrcThreatLevel >= 2, SrcIP," +
                        "\"\"" + ")" +
                        "| where Ip in (" + topMaliciousIp + ")" +
                        "| summarize Count = sum(" + aggField.getTableColumnName() + ") by Ip, TimeSeries = bin(StartTime, 1h)" +
                        makeTimeSeriesCustom(COUNT.getFieldDisplayName(), derivedMetadata.getUpdatedCurrentTimeFrame(), 0, "Ip") +
                        "| project Ip, AggregateField = \"" + labelName + "\", TimeSeries, Count;";

        return query;
    }

    private String getMaliciousIpTrafficResourceInsightsQuery(RequestContext requestContext, TimeCondition timeCondition, Metadata metadata) {
        RequestPayload payload = requestContext.getRequestPayload().orElseThrow();
        String tenantId = requestContext.getTenantId().orElseThrow();
        DerivedMetadata derivedMetadata = requestContext.getDerivedMetadata().orElseThrow();

        List<String> resourceIdList = payload.getFilters() == null ? null : payload.getFilters().stream()
                .filter(f -> "resource_id".equalsIgnoreCase(f.getCategoryName()))
                .flatMap(f -> f.getCategoryValue().stream())
                .map(Object::toString)
                .map(String::toLowerCase)
                .toList();

        String resourceId = "(" + resourceIdList.stream()
                .map(label -> "'" + label + "'")
                .collect(Collectors.joining(", ")) + ")";

        String flows = getMaliciousIpTrafficBaseQuery(payload, tenantId, AGGREGATED_FLOWS, resourceId, "FLOWS", timeCondition, derivedMetadata, metadata);
        String bytes = getMaliciousIpTrafficBaseQuery(payload, tenantId, AGGREGATED_BYTES, resourceId, "BYTES", timeCondition, derivedMetadata, metadata);

        return flows + bytes + " union flows, bytes";
    }

    private String getResourceIdFilter(RequestPayload payload, Metadata metadata, String tenantId, String timeCondition, String labelName, String tableName, String resourceId) {
        return "let " + labelName + " = " + tableName +
                String.format(TENANT_ID, tenantId) +
                timeCondition +
                "| where SrcResId in~ " + resourceId +
                "or DestResId in~ " + resourceId +
                transformFiltersToString(payload.getFilters()) +
                transformGroupingToString(metadata.getSummarizeFields(), metadata.getGroupByFields(), Optional.empty()) + " ; ";
    }

    private String getRiskyServiceTrafficResourceInsightsQuery(RequestContext requestContext, TimeCondition timeCondition, Metadata metadata) {
        RequestPayload payload = requestContext.getRequestPayload().get();
        String tenantId = requestContext.getTenantId().get();
        DerivedMetadata derivedMetadata= requestContext.getDerivedMetadata().get();
        String curr = "";
        String prev = "";
        if (payload.getFilters() != null && payload.getFilters().stream().anyMatch(f -> "resource_id".equalsIgnoreCase(f.getCategoryName()))) {
            List<String> resourceIdList = payload.getFilters().stream()
                    .filter(f -> "resource_id".equalsIgnoreCase(f.getCategoryName()))
                    .flatMap(f -> f.getCategoryValue().stream())
                    .map(Object::toString)
                    .map(String::toLowerCase)
                    .toList();

            String resourceId = "(" + resourceIdList.stream()
                    .map(label -> "'" + label + "'")
                    .collect(Collectors.joining(", ")) + ")";

            curr = getResourceIdFilter(payload, metadata, tenantId, timeCondition.getCurrentTimeCondition(), "curr", derivedMetadata.getCurrentTimeFrameTable(), resourceId);
            prev = getResourceIdFilter(payload, metadata, tenantId, timeCondition.getComparisonTimeCondition(), "prev", derivedMetadata.getComparedTimeFrameTable(), resourceId);
        }
        else {
            curr = getBaseQueryForTimeFrame(payload, metadata, tenantId, timeCondition.getCurrentTimeCondition(), "curr", derivedMetadata.getCurrentTimeFrameTable());
            prev = getBaseQueryForTimeFrame(payload, metadata, tenantId, timeCondition.getComparisonTimeCondition(), "prev", derivedMetadata.getComparedTimeFrameTable());
        }


        return
                curr +
                prev +
                "let rows = " +
                " curr | join kind = leftouter(prev) on " + getGroupByFields(metadata.getGroupByFields()) +
                transformSortingToString(payload.getSortByFields()) +
                tranformPaginationtoString(payload.getPagination());
    }

    private String getExternalDataTranferBaseQuery(String currTable, String prevTable, String tenantId, RequestPayload payload, Fields aggField, String resourceId, String labelName, TimeCondition timeCondition) {
        String CurrTimeFrame = timeCondition.getCurrentTimeCondition();
        String PrevTimeFrame = timeCondition.getComparisonTimeCondition();
        String orderBy = " | order by Count asc";

        String externalCurr = "externalCurr" + labelName;
        String externalPrev = "externalPrev" + labelName;
        String result = labelName.toLowerCase();

        List<Filters> filters = payload.getFilters();

        String query =
                        getResourceBaseQuery(externalCurr, currTable, tenantId, CurrTimeFrame, resourceId, "SrcResId", filters) +
                        "| summarize Count = sum(" + aggField.getTableColumnName() + ") by DestIP, DestDomain;" +

                        getResourceBaseQuery(externalPrev, prevTable, tenantId, PrevTimeFrame, resourceId, "SrcResId", filters) +
                        "| where DestIP in (" + externalCurr + ")" +
                        "| summarize Count1 = sum(" + aggField.getTableColumnName() + ") by DestIP, DestDomain;" +

                        "let " + result + " = " + externalCurr +
                        "| join kind=leftouter " + externalPrev + " on DestIP, DestDomain" +
                        "| project Ip = DestIP, DomainName = DestDomain, Count, Count1, AggregateField = \"" + labelName + "\"" +
                        orderBy +
                        String.format(QUERY_DATA_LIMIT, payload.getPagination() == null ? TOP_QUERIES_LIMIT : payload.getPagination().getRowLimit()) + "; ";
        return query;
    }

    private String getRiskyTrafficTopSubscriptionsWithCountry(RequestContext requestContext, TimeCondition timeCondition, Metadata metadata) {
        RequestPayload payload = requestContext.getRequestPayload().orElseThrow();
        String tenantId = requestContext.getTenantId().orElseThrow();
        DerivedMetadata derivedMetadata = requestContext.getDerivedMetadata().orElseThrow();
        int queryLimit = payload.getPagination() == null ? TOP_QUERIES_LIMIT : payload.getPagination().getRowLimit();

        // Derive top subIds by flows
        // Derive top subIds inbound traffic by flows
        // Derive top subIds outbound traffic by flows
        String subIdsInByFlows = getRiskyTrafficTopSubscriptionsBaseQuery(
                payload,
                tenantId,
                derivedMetadata.getCurrentTimeFrameTable(),
                timeCondition.getCurrentTimeCondition(),
                AGGREGATED_FLOWS,
                TrafficDirection.INBOUND,
                "subIdsInByFlows");
        String subIdsOutByFlows = getRiskyTrafficTopSubscriptionsBaseQuery(
                payload,
                tenantId,
                derivedMetadata.getCurrentTimeFrameTable(),
                timeCondition.getCurrentTimeCondition(),
                AGGREGATED_FLOWS,
                TrafficDirection.OUTBOUND,
                "subIdsOutByFlows");
        String topSubIdsByFlows = "let topSubIdsByFlows = subIdsOutByFlows\n" +
                "| join kind=fullouter (subIdsInByFlows) on SubId\n" +
                "| extend TotalFlowCount = coalesce(CountINBOUND, 0) + coalesce(CountOUTBOUND, 0)\n" +
                "| top " + queryLimit + " by TotalFlowCount\n" +
                "| project SubId = coalesce(SubId, SubId1);";

        // Derive top subIds by bytes
        // Derive top subIds inbound traffic by bytes
        // Derive top subIds outbound traffic by bytes
        String subIdsInByBytes = getRiskyTrafficTopSubscriptionsBaseQuery(
                payload,
                tenantId,
                derivedMetadata.getCurrentTimeFrameTable(),
                timeCondition.getCurrentTimeCondition(),
                AGGREGATED_BYTES,
                TrafficDirection.INBOUND,
                "subIdsInByBytes");
        String subIdsOutByBytes = getRiskyTrafficTopSubscriptionsBaseQuery(
                payload,
                tenantId,
                derivedMetadata.getCurrentTimeFrameTable(),
                timeCondition.getCurrentTimeCondition(),
                AGGREGATED_BYTES,
                TrafficDirection.OUTBOUND,
                "subIdsOutByBytes");
        String topSubIdsByBytes = "let topSubIdsByBytes = subIdsOutByBytes\n" +
                "| join kind=fullouter (subIdsInByBytes) on SubId\n" +
                "| extend TotalBytesCount = coalesce(CountINBOUND, 0) + coalesce(CountOUTBOUND, 0)\n" +
                "| top " + queryLimit + " by TotalBytesCount\n" +
                "| project SubId = coalesce(SubId, SubId1);";

        // Do the following for flows and bytes
        // Inbound Curr - Derive subscription in top subIds with country
        // Outbound Curr - Derive subscription in top subIds with country
        // Inbound Prev - Derive subscription in top subIds with country
        // Outbound Prev - Derive subscription in top subIds with country
        String bySubIdsInCurrFlows = "let bySubIdsInCurrFlows = " +
                getDirectionalBaseQuery(
                        payload,
                        tenantId,
                        derivedMetadata.getCurrentTimeFrameTable(),
                        timeCondition.getCurrentTimeCondition(),
                        TrafficDirection.INBOUND
                ) +
                "| where DestSubId in (topSubIdsByFlows | project SubId) and isempty(SrcSubId)\n" +
                "| summarize TotalFlowCountIn=sum(AggFlowCount) by SubId = DestSubId, AccountName = DestAccountName, Country = SrcCountry;";
        String bySubIdsOutCurrFlows = "let bySubIdsOutCurrFlows = " +
                getDirectionalBaseQuery(
                        payload,
                        tenantId,
                        derivedMetadata.getCurrentTimeFrameTable(),
                        timeCondition.getCurrentTimeCondition(),
                        TrafficDirection.OUTBOUND
                ) +
                "| where SrcSubId in (topSubIdsByFlows | project SubId) and isempty(DestSubId)\n" +
                "| summarize TotalFlowCountOut=sum(AggFlowCount) by SubId = SrcSubId, AccountName = SrcAccountName, Country = DestCountry;";
        String bySubIdsCurrFlows = "let bySubIdsCurrFlows = bySubIdsOutCurrFlows\n" +
                "| join kind=fullouter (bySubIdsInCurrFlows) on SubId, AccountName, Country\n" +
                "| extend TotalFlowCount = coalesce(TotalFlowCountOut, 0) + coalesce(TotalFlowCountIn, 0)\n" +
                "| project SubId = coalesce(SubId, SubId1), AccountName = coalesce(AccountName, AccountName1), Country = coalesce(Country, Country1), TotalFlowCount;";

        String bySubIdsInCurrBytes = "let bySubIdsInCurrBytes = " +
                getDirectionalBaseQuery(
                        payload,
                        tenantId,
                        derivedMetadata.getCurrentTimeFrameTable(),
                        timeCondition.getCurrentTimeCondition(),
                        TrafficDirection.INBOUND
                ) +
                "| where DestSubId in (topSubIdsByBytes | project SubId) and isempty(SrcSubId)\n" +
                "| summarize TotalBytesCountIn=sum(AggByteCount) by SubId = DestSubId, AccountName = DestAccountName, Country = SrcCountry;";
        String bySubIdsOutCurrBytes = "let bySubIdsOutCurrBytes = " +
                getDirectionalBaseQuery(
                        payload,
                        tenantId,
                        derivedMetadata.getCurrentTimeFrameTable(),
                        timeCondition.getCurrentTimeCondition(),
                        TrafficDirection.OUTBOUND
                ) +
                "| where SrcSubId in (topSubIdsByBytes | project SubId) and isempty(DestSubId)\n" +
                "| summarize TotalBytesCountOut=sum(AggByteCount) by SubId = SrcSubId, AccountName = SrcAccountName, Country = DestCountry;";
        String bySubIdsCurrBytes = "let bySubIdsCurrBytes = bySubIdsOutCurrBytes\n" +
                "| join kind=fullouter (bySubIdsInCurrBytes) on SubId, AccountName, Country\n" +
                "| extend TotalBytesCount = coalesce(TotalBytesCountOut, 0) + coalesce(TotalBytesCountIn, 0)\n" +
                "| project SubId = coalesce(SubId, SubId1), AccountName = coalesce(AccountName, AccountName1), Country = coalesce(Country, Country1), TotalBytesCount;";

        String bySubIdsInPrevFlows = "let bySubIdsInPrevFlows = " +
                getDirectionalBaseQuery(
                        payload,
                        tenantId,
                        derivedMetadata.getComparedTimeFrameTable(),
                        timeCondition.getComparisonTimeCondition(),
                        TrafficDirection.INBOUND
                ) +
                "| where DestSubId in (topSubIdsByFlows | project SubId) and isempty(SrcSubId)\n" +
                "| summarize TotalFlowCountInPrev=sum(AggFlowCount) by SubId = DestSubId, AccountName = DestAccountName, Country = SrcCountry;";
        String bySubIdsOutPrevFlows = "let bySubIdsOutPrevFlows = " +
                getDirectionalBaseQuery(
                        payload,
                        tenantId,
                        derivedMetadata.getComparedTimeFrameTable(),
                        timeCondition.getComparisonTimeCondition(),
                        TrafficDirection.OUTBOUND
                ) +
                "| where SrcSubId in (topSubIdsByFlows | project SubId) and isempty(DestSubId)\n" +
                "| summarize TotalFlowCountOutPrev=sum(AggFlowCount) by SubId = SrcSubId, AccountName = SrcAccountName, Country = DestCountry;";
        String bySubIdsPrevFlows = "let bySubIdsPrevFlows = bySubIdsOutPrevFlows\n" +
                "| join kind=fullouter (bySubIdsInPrevFlows) on SubId, Country\n" +
                "| extend TotalFlowCountPrev = coalesce(TotalFlowCountOutPrev, 0) + coalesce(TotalFlowCountInPrev, 0)\n" +
                "| project SubId = coalesce(SubId, SubId1), Country = coalesce(Country, Country1), TotalFlowCountPrev;";

        String bySubIdsInPrevBytes = "let bySubIdsInPrevBytes = " +
                getDirectionalBaseQuery(
                        payload,
                        tenantId,
                        derivedMetadata.getComparedTimeFrameTable(),
                        timeCondition.getComparisonTimeCondition(),
                        TrafficDirection.INBOUND
                ) +
                "| where DestSubId in (topSubIdsByBytes | project SubId) and isempty(SrcSubId)\n" +
                "| summarize TotalBytesCountInPrev=sum(AggByteCount) by SubId = DestSubId, AccountName = DestAccountName, Country = SrcCountry;";
        String bySubIdsOutPrevBytes = "let bySubIdsOutPrevBytes = " +
                getDirectionalBaseQuery(
                        payload,
                        tenantId,
                        derivedMetadata.getComparedTimeFrameTable(),
                        timeCondition.getComparisonTimeCondition(),
                        TrafficDirection.OUTBOUND
                ) +
                "| where SrcSubId in (topSubIdsByBytes | project SubId) and isempty(DestSubId)\n" +
                "| summarize TotalBytesCountOutPrev=sum(AggByteCount) by SubId = SrcSubId, AccountName = SrcAccountName, Country = DestCountry;";
        String bySubIdsPrevBytes = "let bySubIdsPrevBytes = bySubIdsOutPrevBytes\n" +
                "| join kind=fullouter (bySubIdsInPrevBytes) on SubId, Country\n" +
                "| extend TotalBytesCountPrev = coalesce(TotalBytesCountOutPrev, 0) + coalesce(TotalBytesCountInPrev, 0)\n" +
                "| project SubId = coalesce(SubId, SubId1), Country = coalesce(Country, Country1), TotalBytesCountPrev;";

        String subIdsFlows = "let subIdsFlows = bySubIdsCurrFlows\n" +
                "| join kind=leftouter(bySubIdsPrevFlows) on SubId, Country\n" +
                "| project SubId, AccountName, Country, Count=TotalFlowCount, Count1=TotalFlowCountPrev, AggregateField='FLOWS';";
        String subIdsBytes = "let subIdsBytes = bySubIdsCurrBytes\n" +
                "| join kind=leftouter(bySubIdsPrevBytes) on SubId, Country\n" +
                "| project SubId, AccountName, Country, Count=TotalBytesCount, Count1=TotalBytesCountPrev, AggregateField='BYTES';";

        // Collate and expand the data for flows and bytes
        String byFlows = subIdsInByFlows + subIdsOutByFlows + topSubIdsByFlows + bySubIdsInCurrFlows + bySubIdsOutCurrFlows + bySubIdsCurrFlows + bySubIdsInPrevFlows + bySubIdsOutPrevFlows + bySubIdsPrevFlows + subIdsFlows;
        String byBytes = subIdsInByBytes + subIdsOutByBytes + topSubIdsByBytes + bySubIdsInCurrBytes + bySubIdsOutCurrBytes + bySubIdsCurrBytes + bySubIdsInPrevBytes + bySubIdsOutPrevBytes + bySubIdsPrevBytes + subIdsBytes;

        String query = byFlows + byBytes + " union subIdsFlows, subIdsBytes";

        query = query
                .replaceAll("(SourceZone\\s+in\\s+)", "SourceZone in~ ")
                .replaceAll("(DestinationZone\\s+in\\s+)", "DestinationZone in~ ")
                .replaceAll("(SrcRegion\\s+in\\s+)", "SrcRegion in~ ")
                .replaceAll("(DestRegion\\s+in\\s+)", "DestRegion in~ ");

        return query;
    }

    private String getRiskyTrafficTopSubscriptionsBaseQuery(RequestPayload payload,
                                                            String tenantId,
                                                            String table,
                                                            String timeCondition,
                                                            Fields aggColumn,
                                                            TrafficDirection direction,
                                                            String queryAlias) {
        String baseQuery = getDirectionalBaseQuery(payload, tenantId,
                table,
                timeCondition,
                direction);

        String whereClause = direction == TrafficDirection.INBOUND
                ? "| where isempty(SrcSubId) and isnotempty(DestSubId)"
                : "| where isnotempty(SrcSubId) and isempty(DestSubId)";

        String groupByClause = direction == TrafficDirection.INBOUND
                ? "| summarize Count" + direction.name() + "=sum(" + aggColumn.getTableColumnName() + ") by SubId = DestSubId;"
                : "| summarize Count" + direction.name() + "=sum(" + aggColumn.getTableColumnName() + ") by SubId = SrcSubId;";

        return "let " + queryAlias + " = " +
                baseQuery + "\n" +
                whereClause + "\n" +
                groupByClause;
    }

    private String getExternalDataTransfredResourceInsightsQuery(RequestContext requestContext, TimeCondition timeCondition) {
        RequestPayload payload = requestContext.getRequestPayload().orElseThrow();
        String tenantId = requestContext.getTenantId().orElseThrow();
        DerivedMetadata derivedMetadata = requestContext.getDerivedMetadata().orElseThrow();

        List<String> resourceIdList = payload.getFilters() == null ? null : payload.getFilters().stream()
                .filter(f -> "resource_id".equalsIgnoreCase(f.getCategoryName()))
                .flatMap(f -> f.getCategoryValue().stream())
                .map(Object::toString)
                .map(String::toLowerCase)
                .toList();

        String resourceId = "(" + resourceIdList.stream()
                .map(label -> "'" + label + "'")
                .collect(Collectors.joining(", ")) + ")";

        String flows = getExternalDataTranferBaseQuery(derivedMetadata.getCurrentTimeFrameTable(), derivedMetadata.getComparedTimeFrameTable(), tenantId, payload, AGGREGATED_FLOWS, resourceId, "FLOWS", timeCondition);
        String bytes = getExternalDataTranferBaseQuery(derivedMetadata.getCurrentTimeFrameTable(), derivedMetadata.getComparedTimeFrameTable(), tenantId, payload, AGGREGATED_BYTES, resourceId, "BYTES", timeCondition);

        return flows + bytes + " union flows, bytes";
    }


    /* //////////////////////////////////////////
     * Cross Country Insights
     * //////////////////////////////////////////*/

    private String buildCrossCountryBaseQuery(RequestPayload payload, String tenantId, String table, String timeCondition, Fields aggColumn, TrafficDirection direction, String queryAlias, boolean withRegion, String labelName) {
        String baseQuery = getDirectionalBaseQuery(payload, tenantId, table, timeCondition, direction);

        String whereClause;
        String groupByClause;

        if (direction == TrafficDirection.INBOUND) { // INBOUND
            if (withRegion) { // with region
                whereClause = "| where SrcCountry in (topCountries" + labelName + " | project Country) " +
                        "| where isempty(SrcSubId) and isnotempty(DestSubId)";
                groupByClause = "| summarize TotalCountIn = sum(" + aggColumn.getTableColumnName() +
                        ") by Zone = DestinationZone, Region = DestRegion, Country = SrcCountry" +
                        " | extend TrafficDirection = '" + TrafficDirection.INBOUND + "';";
            } else { // without region
                whereClause = "| where isnotempty(SrcCountry) " +
                        "| where isempty(SrcSubId) and isnotempty(DestSubId)";
                groupByClause = "| summarize TotalCountIn = sum(" + aggColumn.getTableColumnName() +
                        ") by Country = SrcCountry" +
                        " | extend TrafficDirection = '" + TrafficDirection.INBOUND + "';";
            }
        } else { // OUTBOUND
            if (withRegion) { // with region
                whereClause = "| where DestCountry in (topCountries" + labelName + " | project Country) " +
                        "| where isnotempty(SrcSubId) and isempty(DestSubId)";
                groupByClause = "| summarize TotalCountOut = sum(" + aggColumn.getTableColumnName() +
                        ") by Zone = SourceZone, Region = SrcRegion, Country = DestCountry" +
                        " | extend TrafficDirection = '" + TrafficDirection.OUTBOUND + "';";
            } else { // without region
                whereClause = "| where isnotempty(DestCountry) " +
                        "| where isnotempty(SrcSubId) and isempty(DestSubId)";
                groupByClause = "| summarize TotalCountOut = sum(" + aggColumn.getTableColumnName() +
                        ") by Country = DestCountry" +
                        " | extend TrafficDirection = '" + TrafficDirection.OUTBOUND + "';";
            }
        }

        baseQuery = baseQuery
            .replaceAll("(SourceZone\\s+in\\s+)", "SourceZone in~ ")
            .replaceAll("(DestinationZone\\s+in\\s+)", "DestinationZone in~ ")
            .replaceAll("(SrcRegion\\s+in\\s+)", "SrcRegion in~ ")
            .replaceAll("(DestRegion\\s+in\\s+)", "DestRegion in~ ");

        return "let " + queryAlias + " = " +
                baseQuery + "\n" +
                whereClause + "\n" +
                groupByClause;
    }

    private String getCrossCountryMapByCountryBaseQuery (RequestContext requestContext, Metadata metadata, TimeCondition timeCondition, Fields aggfield, String labelName ) {
        RequestPayload payload = requestContext.getRequestPayload().get();
        String tenantId = requestContext.getTenantId().get();
        DerivedMetadata derivedMetadata= requestContext.getDerivedMetadata().get();

        Optional<String> order = payload.getSortByFields().stream()
                .map(SortByFields::getOrder)
                .findFirst();

        String sorting = order.map(o -> "| top 5 by Count " + o).orElse("");

        return
                buildCrossCountryBaseQuery(payload, tenantId, derivedMetadata.getCurrentTimeFrameTable(), timeCondition.getCurrentTimeCondition(), aggfield, TrafficDirection.OUTBOUND, "topCountriesOut" + labelName, false, labelName) +

                buildCrossCountryBaseQuery(payload, tenantId, derivedMetadata.getCurrentTimeFrameTable(), timeCondition.getCurrentTimeCondition(), aggfield, TrafficDirection.INBOUND, "topCountriesIn" + labelName, false, labelName) +

                "let topCountries" + labelName + "  = topCountriesOut" + labelName +
                "| join kind=fullouter (topCountriesIn" + labelName + ") on Country " +
                "| extend Count = coalesce(TotalCountIn, 0) + coalesce(TotalCountOut, 0)" +
                 sorting +
                "| project Country = coalesce(Country, Country1);";
    }

    private String buildAggregateQuery(String labelName, String currQuery, String prevQuery) {
        String query = "let " + labelName + " = " + currQuery +
                "| join kind=fullouter (" + prevQuery + ") on Zone, Region, Country" +
                "| extend Count = coalesce(TotalCountOut, 0) + coalesce(TotalCountIn, 0)" +
                "| project" +
                "    Zone = coalesce(Zone, Zone1)," +
                "    Region = coalesce(Region, Region1)," +
                "    Country = coalesce(Country, Country1)," +
                "    Count;";

        return query;
    }

    private String buildSeperateQuery(String labelName, String currQuery, String prevQuery) {
        String query = "let " + labelName + " = " + currQuery +
                "| join kind=fullouter (" + prevQuery + ") on TrafficDirection" +
                "| extend Count = coalesce(TotalCountOut, 0) + coalesce(TotalCountIn, 0)" +
                "| project" +
                "   Zone = coalesce(Zone, Zone1)," +
                "   Region = coalesce(Region, Region1)," +
                "   Country = coalesce(Country, Country1)," +
                "   Count," +
                "   TrafficDirection = coalesce(TrafficDirection, TrafficDirection1);";

        return query;
    }

    private String getCrossCountryMapbyRegionBaseQuery(RequestContext requestContext, Metadata metadata, TimeCondition timeCondition, Fields aggfield, String labelName ) {
        RequestPayload payload = requestContext.getRequestPayload().get();
        String tenantId = requestContext.getTenantId().get();
        DerivedMetadata derivedMetadata= requestContext.getDerivedMetadata().get();

        return
                buildCrossCountryBaseQuery(payload, tenantId, derivedMetadata.getCurrentTimeFrameTable(), timeCondition.getCurrentTimeCondition(), aggfield, TrafficDirection.OUTBOUND, "byRegionCountriesOutCurr" + labelName, true, labelName) +
                buildCrossCountryBaseQuery(payload, tenantId, derivedMetadata.getComparedTimeFrameTable(), timeCondition.getComparisonTimeCondition(), aggfield, TrafficDirection.OUTBOUND, "byRegionCountriesOutPrev" + labelName, true, labelName) +

                buildCrossCountryBaseQuery(payload, tenantId, derivedMetadata.getCurrentTimeFrameTable(), timeCondition.getCurrentTimeCondition(), aggfield, TrafficDirection.INBOUND, "byRegionCountriesInCurr" + labelName, true, labelName) +
                buildCrossCountryBaseQuery(payload, tenantId, derivedMetadata.getComparedTimeFrameTable(), timeCondition.getComparisonTimeCondition(), aggfield, TrafficDirection.INBOUND, "byRegionCountriesInPrev" + labelName, true, labelName) +

                buildAggregateQuery("aggregateCurr" + labelName,"byRegionCountriesOutCurr" + labelName, "byRegionCountriesInCurr" + labelName) +
                buildAggregateQuery("aggregatePrev" + labelName,"byRegionCountriesOutPrev" + labelName, "byRegionCountriesInPrev" + labelName) +

                "let aggregate" + labelName + "  = aggregateCurr" + labelName +
                "| join kind=fullouter (aggregatePrev" + labelName + ") on Zone, Region, Country" +
                "| project" +
                "    Zone = coalesce(Zone, Zone1)," +
                "    Region = coalesce(Region, Region1)," +
                "    Country = coalesce(Country, Country1)," +
                "    Count," +
                "    Count1," +
                "    TrafficDirection = '" + TrafficDirection.BOTH + "'," +
                "    AggregateField = \"" + labelName + "\";" +

                buildSeperateQuery("seperateCurr" + labelName, "byRegionCountriesOutCurr" + labelName, "byRegionCountriesInCurr" + labelName) +
                buildSeperateQuery("seperatePrev" + labelName, "byRegionCountriesOutPrev" + labelName, "byRegionCountriesInPrev" + labelName) +

                "let seperate" + labelName + " = seperateCurr" + labelName +
                "| join kind=fullouter (seperatePrev" + labelName + ") on Zone, Region, Country, TrafficDirection" +
                "| project" +
                "    Zone = coalesce(Zone, Zone1)," +
                "    Region = coalesce(Region, Region1)," +
                "    Country = coalesce(Country, Country1)," +
                "    Count," +
                "    Count1," +
                "    TrafficDirection = coalesce(TrafficDirection, TrafficDirection1)," +
                "    AggregateField = \"" + labelName + "\";" +

                "let " + labelName + " = union aggregate" + labelName + ", seperate" + labelName + ";";
    }

    private String getTrafficActivityAcrossCountriesCrossCountryQuery(RequestContext requestContext, TimeCondition timeCondition, Metadata metadata) {
        String topCountriesflows = getCrossCountryMapByCountryBaseQuery(requestContext, metadata, timeCondition, AGGREGATED_FLOWS, "FLOWS");
        String byRegionCountriesflows = getCrossCountryMapbyRegionBaseQuery(requestContext, metadata, timeCondition, AGGREGATED_FLOWS, "FLOWS");
        String topCountriesbytes = getCrossCountryMapByCountryBaseQuery(requestContext, metadata, timeCondition, AGGREGATED_BYTES, "BYTES");
        String byRegionCountriesbytes = getCrossCountryMapbyRegionBaseQuery(requestContext, metadata, timeCondition, AGGREGATED_BYTES, "BYTES");
        String union = "union FLOWS, BYTES;";
        return topCountriesflows + byRegionCountriesflows + topCountriesbytes + byRegionCountriesbytes + union;
    }

    private String getCrossRegionCustomFilterBaseQuery(
        RequestPayload payload, String tenantId, String tableName, String timeCondition, Fields aggField) {

                String zone = payload.getFilters() == null ? null : payload.getFilters().stream()
                        .filter(f -> "zone".equalsIgnoreCase(f.getCategoryName()))
                        .flatMap(f -> f.getCategoryValue().stream())
                        .map(Object::toString)
                        .findFirst()
                        .orElse(null);

                String region = payload.getFilters() == null ? null : payload.getFilters().stream()
                        .filter(f -> "region".equalsIgnoreCase(f.getCategoryName()))
                        .flatMap(f -> f.getCategoryValue().stream())
                        .map(Object::toString)
                        .findFirst()
                        .orElse(null);

                // Create a base filter query
                String filterQuery = tableName + String.format(TENANT_ID, tenantId) + timeCondition;

                // Some filters have custom column names, find out which filters to update
                List<Filters> filterListToUpdate = payload.getFilters() != null ? 
                    new ArrayList<>(payload.getFilters()) : new ArrayList<>();

                if (zone != null && region != null) {
                    filterQuery += "| where SourceZone in~ ('" + zone + "') and SrcRegion in~ ('" + region + "')";
                    filterQuery += " or DestinationZone in~ ('" + zone + "') and DestRegion in~ ('" + region + "')";
                    filterListToUpdate.removeIf(f -> "region".equalsIgnoreCase(f.getCategoryName()));
                    filterListToUpdate.removeIf(f -> "zone".equalsIgnoreCase(f.getCategoryName()));
                }

                // Convert remaining filters to KQL string
                filterQuery += transformFiltersToString(filterListToUpdate);

                return filterQuery +
                        "| where isnotempty(SourceZone) and isnotempty(DestinationZone)" +
                        "| where isnotempty(SrcRegion) and isnotempty(DestRegion)" +
                        "| where (SourceZone != DestinationZone) or  (SourceZone == DestinationZone and SrcRegion != DestRegion)" +
                        "| summarize Count=sum(" + aggField.getTableColumnName() + ") by SourceZone, SrcRegion, DestinationZone, DestRegion;";
    }

    private String getCrossRegionCountryInsightsBaseQuery(RequestContext requestContext, Metadata metadata, TimeCondition timeCondition, Fields aggField, String labelName) {
        DerivedMetadata derivedMetadata = requestContext.getDerivedMetadata().get();
        RequestPayload payload = requestContext.getRequestPayload().get();
        String tenantId = requestContext.getTenantId().get();

        return "let by" + aggField.getFieldKey() + "curr = "  +
                getCrossRegionCustomFilterBaseQuery(
                        payload, 
                        tenantId, 
                        derivedMetadata.getCurrentTimeFrameTable(), 
                        timeCondition.getCurrentTimeCondition(),
                        aggField) +

                "let by" + aggField.getFieldKey() + "prev = "  +
                getCrossRegionCustomFilterBaseQuery(
                        payload, 
                        tenantId, 
                        derivedMetadata.getComparedTimeFrameTable(), 
                        timeCondition.getComparisonTimeCondition(),
                        aggField) +

                "let by" + aggField.getFieldKey() + " = "  +
                "by" + aggField.getFieldKey() + "curr" +
                "| join kind=leftouter (" + "by" + aggField.getFieldKey() + "prev) on SourceZone, SrcRegion, DestinationZone, DestRegion" +
                "| extend AggregateField = " + "'" + labelName + "'" +
                "| project SourceZone, SrcRegion, DestinationZone, DestRegion, Count, Count1, AggregateField " +
                String.format(QUERY_DATA_LIMIT, payload.getPagination() == null ? TOP_QUERIES_LIMIT : payload.getPagination().getRowLimit()) + " ; ";
    }

    private String getCrossRegionTrafficCountryInsightsQuery(RequestContext requestContext, TimeCondition timeCondition, Metadata metadata) {
        SortByFields sortByField = SortByFields.builder().field(COUNT.getFieldKey()).order("desc").build();
        String flows = getCrossRegionCountryInsightsBaseQuery(requestContext, metadata, timeCondition, AGGREGATED_FLOWS, "FLOWS");
        String bytes = getCrossRegionCountryInsightsBaseQuery(requestContext, metadata, timeCondition, AGGREGATED_BYTES, "BYTES");
        String union = "union byflows, bybytes ";
        String sorting = transformSortingToStringIntermediate(List.of(sortByField));

        return flows + bytes + union + sorting;
    }

    // traverse each filter in the payload, transform it to a kql readable string format
    // each new filter is a new WHERE clause, multiple values in a filter is an IN clause
    private String transformFiltersToString(List<Filters> filters) {
        if(filters != null && !filters.isEmpty()) {
            return filters.stream()
                    .filter(filterQuery -> !TRAFFIC_DIRECTION.getFieldKey().equalsIgnoreCase(filterQuery.getCategoryName()))
                    .filter(filterQuery -> !"category".equalsIgnoreCase(filterQuery.getCategoryName()))
                    .filter(filterQuery -> !"resource_id".equalsIgnoreCase(filterQuery.getCategoryName()))
                    .map(filter -> "| where " + Fields.getTableColumnNameByFieldKey(filter.getCategoryName()) + " in (" +
                            filter.getCategoryValue().stream()
                                    .map(value -> "number".equals(filter.getCategoryType()) ? value.toString() : "'" + value + "'")
                                    .collect(Collectors.joining(", ")) +
                            ")")
                    .collect(Collectors.joining(" "));
        }
        return "";
    }

    // traverse each filter in the payload, transform it to a kql readable string format
    // each new filter is a new WHERE clause, multiple values in a filter is an IN clause
    // The direction_type dictates either to pick source or destination filters
    private String transformDirectionalFiltersToString(List<Filters> filters) {
        TrafficDirection direction = ApplicationUtils.getTrafficDirection(filters);
        if(filters != null && !filters.isEmpty()) {
            return filters.stream()
                    .filter(filterQuery -> !TRAFFIC_DIRECTION.getFieldKey().equalsIgnoreCase(filterQuery.getCategoryName()))
                    .filter(filterQuery -> !"category".equalsIgnoreCase(filterQuery.getCategoryName()))
                    .filter(filterQuery -> !"resource_id".equalsIgnoreCase(filterQuery.getCategoryName()))
                    .map(filter -> {
                        Fields fieldForFilter = Fields.getFieldByFieldKey(filter.getCategoryName()).resolve(direction);
                        return "| where " + fieldForFilter.getTableColumnName() + " in (" +
                            filter.getCategoryValue().stream()
                                    .map(value -> "number".equals(filter.getCategoryType()) ? value.toString() : "'" + value + "'")
                                    .collect(Collectors.joining(", ")) +
                            ")";
                    })
                    .collect(Collectors.joining(" "));
        }
        return "";
    }

    // get comma separated group by fields
    private String getGroupByFields(List<Fields> groupByFields) {
        return groupByFields.stream()
                .map(Fields::getTableColumnName)
                .collect(Collectors.joining(", "));
    }
    // summarize based on widget metadata, transform to kql readable string
    private String transformGroupingToString(List<Fields> summarizeFields, List<Fields> groupByFields, Optional<String> label) {
        if(summarizeFields != null && !summarizeFields.isEmpty()) {
            return "| summarize " +
                    summarizeFields.stream().map(field -> {
                        String fieldLabel = label.orElse(field.getTableColumnName());
                        return fieldLabel + " = " + "sum(" + field.getTableColumnName() + ")";
                    }).collect(Collectors.joining(", ")) +
                    " by " +
                    groupByFields.stream()
                            .map(Fields::getTableColumnName)
                            .collect(Collectors.joining(", "));
        }
        return "";
    }
    // transform payload's sort order to kql readable string
    private String transformSortingToString(List<SortByFields> sortByFields) {
        return "|sort by " +
                sortByFields.stream()
                        .map(field -> Fields.getTableColumnNameByFieldKey(field.getField()) + " " + field.getOrder()).collect(Collectors.joining(", ")) + " ; ";

    }

    private String transformSortingToStringIntermediate(List<SortByFields> sortByFields) {
        return "|sort by " +
                sortByFields.stream()
                        .map(field -> Fields.getTableColumnNameByFieldKey(field.getField()) + " " + field.getOrder()).collect(Collectors.joining(", "));

    }

    //convert pagination in the payload to a string.
    //get total row count for query -> then return the page requested based on row number
    //default sort order is needed for idempotency.
    private String tranformPaginationtoString(Pagination pagination) {
        int startWindow = ((pagination.getPageNumber()-1)* pagination.getRowLimit())+1;
        String paginationQuery =
                "let totalRows = toscalar(rows | count);" +
                "rows " +
                "| serialize rn = row_number()";

        if(pagination.getRowLimit() > 0) {
            paginationQuery +=
                    "| where rn between (" + startWindow+" .. " + (startWindow+ pagination.getRowLimit()-1) + ")";
        }
        paginationQuery += "| extend "+ TOTAL_ROWS.getTableColumnName() + " = totalRows";

        return paginationQuery;
    }

    //Create a time-series of data with a default value if the datapoint doesn't exist
    private String makeTimeSeries(String fieldName, TimeFrame timeFrame, Object defaultValue) {
        return "| make-series " + fieldName + "  = sum(" + fieldName + ") default = " + defaultValue +
                " on TimeSeries in range(datetime('" + timeFrame.getStartTime() + "'), " + "datetime('" + timeFrame.getEndTime() + "'), 1h) by SrcIP " +
                "| mv-expand TimeSeries, " + fieldName;
    }

    private String makeTimeSeriesCustom(String fieldName, TimeFrame timeFrame, Object defaultValue, String groupBy) {
        return "| make-series " + fieldName + "  = sum(" + fieldName + ") default = " + defaultValue +
                " on TimeSeries in range(datetime('" + timeFrame.getStartTime() + "'), " + "datetime('" + timeFrame.getEndTime() + "'), 1h) by " + groupBy + "   " +
                "| mv-expand TimeSeries, " + fieldName;
    }

    private String getCspRegions(RequestContext requestContext, TimeCondition timeCondition, Metadata metadata) {
        RequestPayload payload = requestContext.getRequestPayload().orElse(null);
        String tenantId = requestContext.getTenantId().orElseThrow();
        DerivedMetadata derivedMetadata = requestContext.getDerivedMetadata().orElseThrow();
        String tableName = derivedMetadata.getCurrentTimeFrameTable();

        // Use Fields enum for all table column names
        String srcSubId = Fields.SOURCE_SUBSCRIPTION_ID.getTableColumnName();
        String sourceZone = Fields.SOURCE_ZONE.getTableColumnName();
        String srcRegion = Fields.SOURCE_REGION.getTableColumnName();
        String destSubId = Fields.DESTINATION_SUBSCRIPTION_ID.getTableColumnName();
        String destinationZone = Fields.DESTINATION_ZONE.getTableColumnName();
        String destRegion = Fields.DESTINATION_REGION.getTableColumnName();

        String baseQuery = getBaseQuery(payload != null ? payload : RequestPayload.builder().build(), tenantId, tableName,
            timeCondition.getCurrentTimeCondition());

        String getCspRegionsQuery = "let t = " + baseQuery + ";" +
            "t " +
            "| where isnotempty(" + srcSubId + ") and isnotempty(" + sourceZone + ") and isnotempty(" + srcRegion + ") " +
            "| project Region = " + srcRegion + ", Zone = " + sourceZone + " | union (t " +
            "| where isnotempty(" + destSubId + ") and isnotempty(" + destinationZone + ") and isnotempty(" + destRegion + ") " +
            "| project Region = " + destRegion + ", Zone = " + destinationZone + ") | distinct Zone, Region";
        return getCspRegionsQuery;
    }

    // get the risky service names for insights
    private String getRiskyServiceNames(RequestContext requestContext, TimeCondition timeCondition, Metadata metadata) {
        RequestPayload payload = requestContext.getRequestPayload().orElseThrow();
        String tenantId = requestContext.getTenantId().orElseThrow();
        DerivedMetadata derivedMetadata = requestContext.getDerivedMetadata().orElseThrow();
        String tableName = derivedMetadata.getCurrentTimeFrameTable();
        String baseQuery = getBaseQuery(payload, tenantId, tableName, timeCondition.getCurrentTimeCondition());
        return baseQuery + " | distinct ServiceName, Port, Proto";
    }
}

