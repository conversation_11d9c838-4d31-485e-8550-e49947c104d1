package com.illumio.data.utils;

import com.illumio.data.model.FlowValue;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class OverwriteNullFields {

    // Avoiding Reflection because this is a high traffic flow - Reflection doesn't work well with high volume
    public void replaceNullValuesInAggregate(FlowValue flowValue, FlowValue aggregate) {
        if (aggregate.getSrcIP() == null && flowValue.getSrcIP() != null) {
            aggregate.setSrcIP(flowValue.getSrcIP());
        }

        if (aggregate.getSrcId() == null && flowValue.getSrcId() != null) {
            aggregate.setSrcId(flowValue.getSrcId());
        }

        if (aggregate.getCSSrcId() == null && flowValue.getCSSrcId() != null) {
            aggregate.setCSSrcId(flowValue.getCSSrcId());
        }

        if (aggregate.getDestIP() == null && flowValue.getDestIP() != null) {
            aggregate.setDestIP(flowValue.getDestIP());
        }

        if (aggregate.getDestId() == null && flowValue.getDestId() != null) {
            aggregate.setDestId(flowValue.getDestId());
        }

        if (aggregate.getCSDestId() == null && flowValue.getCSDestId() != null) {
            aggregate.setCSDestId(flowValue.getCSDestId());
        }

        if (aggregate.getPort() == null && flowValue.getPort() != null) {
            aggregate.setPort(flowValue.getPort());
        }

        if (aggregate.getProto() == null && flowValue.getProto() != null) {
            aggregate.setProto(flowValue.getProto());
        }

        if (aggregate.getSentBytes() == null && flowValue.getSentBytes() != null) {
            aggregate.setSentBytes(flowValue.getSentBytes());
        }

        if (aggregate.getReceivedBytes() == null && flowValue.getReceivedBytes() != null) {
            aggregate.setReceivedBytes(flowValue.getReceivedBytes());
        }

        if (aggregate.getIllumioTenantId() == null && flowValue.getIllumioTenantId() != null) {
            aggregate.setIllumioTenantId(flowValue.getIllumioTenantId());
        }

        if (aggregate.getSrcTenantId() == null && flowValue.getSrcTenantId() != null) {
            aggregate.setSrcTenantId(flowValue.getSrcTenantId());
        }

        if (aggregate.getSrcSubId() == null && flowValue.getSrcSubId() != null) {
            aggregate.setSrcSubId(flowValue.getSrcSubId());
        }

        if (aggregate.getSrcRegion() == null && flowValue.getSrcRegion() != null) {
            aggregate.setSrcRegion(flowValue.getSrcRegion());
        }

        if (aggregate.getSrcResId() == null && flowValue.getSrcResId() != null) {
            aggregate.setSrcResId(flowValue.getSrcResId());
        }

        if (aggregate.getSrcVnetId() == null && flowValue.getSrcVnetId() != null) {
            aggregate.setSrcVnetId(flowValue.getSrcVnetId());
        }

        if (aggregate.getSourceUserName() == null && flowValue.getSourceUserName() != null) {
            aggregate.setSourceUserName(flowValue.getSourceUserName());
        }

        if (aggregate.getDestTenantId() == null && flowValue.getDestTenantId() != null) {
            aggregate.setDestTenantId(flowValue.getDestTenantId());
        }

        if (aggregate.getDestSubId() == null && flowValue.getDestSubId() != null) {
            aggregate.setDestSubId(flowValue.getDestSubId());
        }

        if (aggregate.getDestRegion() == null && flowValue.getDestRegion() != null) {
            aggregate.setDestRegion(flowValue.getDestRegion());
        }

        if (aggregate.getDestResId() == null && flowValue.getDestResId() != null) {
            aggregate.setDestResId(flowValue.getDestResId());
        }

        if (aggregate.getDestVnetId() == null && flowValue.getDestVnetId() != null) {
            aggregate.setDestVnetId(flowValue.getDestVnetId());
        }

        if (aggregate.getDestinationUserName() == null && flowValue.getDestinationUserName() != null) {
            aggregate.setDestinationUserName(flowValue.getDestinationUserName());
        }

        if (aggregate.getSrcFlowType() == null && flowValue.getSrcFlowType() != null) {
            aggregate.setSrcFlowType(flowValue.getSrcFlowType());
        }

        if (aggregate.getDestFlowType() == null && flowValue.getDestFlowType() != null) {
            aggregate.setDestFlowType(flowValue.getDestFlowType());
        }

        if (aggregate.getSrcDeviceId() == null && flowValue.getSrcDeviceId() != null) {
            aggregate.setSrcDeviceId(flowValue.getSrcDeviceId());
        }

        if (aggregate.getSrcFirewallId() == null && flowValue.getSrcFirewallId() != null) {
            aggregate.setSrcFirewallId(flowValue.getSrcFirewallId());
        }

        if (aggregate.getSourceUserId() == null && flowValue.getSourceUserId() != null) {
            aggregate.setSourceUserId(flowValue.getSourceUserId());
        }

        if (aggregate.getDestDeviceId() == null && flowValue.getDestDeviceId() != null) {
            aggregate.setDestDeviceId(flowValue.getDestDeviceId());
        }

        if (aggregate.getDestFirewallId() == null && flowValue.getDestFirewallId() != null) {
            aggregate.setDestFirewallId(flowValue.getDestFirewallId());
        }

        if (aggregate.getDestinationUserId() == null && flowValue.getDestinationUserId() != null) {
            aggregate.setDestinationUserId(flowValue.getDestinationUserId());
        }

        if (aggregate.getSrcResourceType() == null && flowValue.getSrcResourceType() != null) {
            aggregate.setSrcResourceType(flowValue.getSrcResourceType());
        }

        if (aggregate.getDestResourceType() == null && flowValue.getDestResourceType() != null) {
            aggregate.setDestResourceType(flowValue.getDestResourceType());
        }

        if (aggregate.getSrcThreatLevel() == null && flowValue.getSrcThreatLevel() != null) {
            aggregate.setSrcThreatLevel(flowValue.getSrcThreatLevel());
        }

        if (aggregate.getDestThreatLevel() == null && flowValue.getDestThreatLevel() != null) {
            aggregate.setDestThreatLevel(flowValue.getDestThreatLevel());
        }

        if (aggregate.getSrcIsWellknown() == null && flowValue.getSrcIsWellknown() != null) {
            aggregate.setSrcIsWellknown(flowValue.getSrcIsWellknown());
        }

        if (aggregate.getDestIsWellknown() == null && flowValue.getDestIsWellknown() != null) {
            aggregate.setDestIsWellknown(flowValue.getDestIsWellknown());
        }

        if (aggregate.getSrcDomain() == null && flowValue.getSrcDomain() != null) {
            aggregate.setSrcDomain(flowValue.getSrcDomain());
        }

        if (aggregate.getDestDomain() == null && flowValue.getDestDomain() != null) {
            aggregate.setDestDomain(flowValue.getDestDomain());
        }

        if (aggregate.getSrcCountry() == null && flowValue.getSrcCountry() != null) {
            aggregate.setSrcCountry(flowValue.getSrcCountry());
        }

        if (aggregate.getDestCountry() == null && flowValue.getDestCountry() != null) {
            aggregate.setDestCountry(flowValue.getDestCountry());
        }

        if (aggregate.getSrcCity() == null && flowValue.getSrcCity() != null) {
            aggregate.setSrcCity(flowValue.getSrcCity());
        }

        if (aggregate.getDestCity() == null && flowValue.getDestCity() != null) {
            aggregate.setDestCity(flowValue.getDestCity());
        }

        if (aggregate.getSrcCloudProvider() == null && flowValue.getSrcCloudProvider() != null) {
            aggregate.setSrcCloudProvider(flowValue.getSrcCloudProvider());
        }

        if (aggregate.getDestCloudProvider() == null && flowValue.getDestCloudProvider() != null) {
            aggregate.setDestCloudProvider(flowValue.getDestCloudProvider());
        }

        if (aggregate.getSourceHostName() == null && flowValue.getSourceHostName() != null) {
            aggregate.setSourceHostName(flowValue.getSourceHostName());
        }

        if (aggregate.getSourceNTDomain() == null && flowValue.getSourceNTDomain() != null) {
            aggregate.setSourceNTDomain(flowValue.getSourceNTDomain());
        }

        if (aggregate.getSourceProcessId() == null && flowValue.getSourceProcessId() != null) {
            aggregate.setSourceProcessId(flowValue.getSourceProcessId());
        }

        if (aggregate.getSourceProcessName() == null && flowValue.getSourceProcessName() != null) {
            aggregate.setSourceProcessName(flowValue.getSourceProcessName());
        }

        if (aggregate.getSourceMACAddress() == null && flowValue.getSourceMACAddress() != null) {
            aggregate.setSourceMACAddress(flowValue.getSourceMACAddress());
        }

        if (aggregate.getSourceUserPrivileges() == null && flowValue.getSourceUserPrivileges() != null) {
            aggregate.setSourceUserPrivileges(flowValue.getSourceUserPrivileges());
        }

        if (aggregate.getDeviceAction() == null && flowValue.getDeviceAction() != null) {
            aggregate.setDeviceAction(flowValue.getDeviceAction());
        }

        if (aggregate.getDeviceAddress() == null && flowValue.getDeviceAddress() != null) {
            aggregate.setDeviceAddress(flowValue.getDeviceAddress());
        }

        if (aggregate.getDeviceEventClassId() == null && flowValue.getDeviceEventClassId() != null) {
            aggregate.setDeviceEventClassId(flowValue.getDeviceEventClassId());
        }

        if (aggregate.getDestinationDnsDomain() == null && flowValue.getDestinationDnsDomain() != null) {
            aggregate.setDestinationDnsDomain(flowValue.getDestinationDnsDomain());
        }

        if (aggregate.getDestinationHostName() == null && flowValue.getDestinationHostName() != null) {
            aggregate.setDestinationHostName(flowValue.getDestinationHostName());
        }

        if (aggregate.getDestinationNTDomain() == null && flowValue.getDestinationNTDomain() != null) {
            aggregate.setDestinationNTDomain(flowValue.getDestinationNTDomain());
        }

        if (aggregate.getDestinationProcessId() == null && flowValue.getDestinationProcessId() != null) {
            aggregate.setDestinationProcessId(flowValue.getDestinationProcessId());
        }

        if (aggregate.getDestinationProcessName() == null && flowValue.getDestinationProcessName() != null) {
            aggregate.setDestinationProcessName(flowValue.getDestinationProcessName());
        }

        if (aggregate.getDestinationServiceName() == null && flowValue.getDestinationServiceName() != null) {
            aggregate.setDestinationServiceName(flowValue.getDestinationServiceName());
        }

        if (aggregate.getDestinationTranslatedAddress() == null && flowValue.getDestinationTranslatedAddress() != null) {
            aggregate.setDestinationTranslatedAddress(flowValue.getDestinationTranslatedAddress());
        }

        if (aggregate.getDestinationMACAddress() == null && flowValue.getDestinationMACAddress() != null) {
            aggregate.setDestinationMACAddress(flowValue.getDestinationMACAddress());
        }

        if (aggregate.getLogSeverity() == null && flowValue.getLogSeverity() != null) {
            aggregate.setLogSeverity(flowValue.getLogSeverity());
        }

        if (aggregate.getMaliciousIP() == null && flowValue.getMaliciousIP() != null) {
            aggregate.setMaliciousIP(flowValue.getMaliciousIP());
        }

        if (aggregate.getMaliciousIPCountry() == null && flowValue.getMaliciousIPCountry() != null) {
            aggregate.setMaliciousIPCountry(flowValue.getMaliciousIPCountry());
        }

        if (aggregate.getMaliciousIPLatitude() == null && flowValue.getMaliciousIPLatitude() != null) {
            aggregate.setMaliciousIPLatitude(flowValue.getMaliciousIPLatitude());
        }

        if (aggregate.getMaliciousIPLongitude() == null && flowValue.getMaliciousIPLongitude() != null) {
            aggregate.setMaliciousIPLongitude(flowValue.getMaliciousIPLongitude());
        }

        if (aggregate.getLawTenantId() == null && flowValue.getLawTenantId() != null) {
            aggregate.setLawTenantId(flowValue.getLawTenantId());
        }

        if (aggregate.getThreatConfidence() == null && flowValue.getThreatConfidence() != null) {
            aggregate.setThreatConfidence(flowValue.getThreatConfidence());
        }

        if (aggregate.getThreatDescription() == null && flowValue.getThreatDescription() != null) {
            aggregate.setThreatDescription(flowValue.getThreatDescription());
        }

        if (aggregate.getThreatSeverity() == null && flowValue.getThreatSeverity() != null) {
            aggregate.setThreatSeverity(flowValue.getThreatSeverity());
        }

        if (aggregate.getStartTime() == null && flowValue.getStartTime() != null) {
            aggregate.setStartTime(flowValue.getStartTime());
        }

        if (aggregate.getEndTime() == null && flowValue.getEndTime() != null) {
            aggregate.setEndTime(flowValue.getEndTime());
        }

        if (aggregate.getSourceDnsDomain() == null && flowValue.getSourceDnsDomain() != null) {
            aggregate.setSourceDnsDomain(flowValue.getSourceDnsDomain());
        }

        if (aggregate.getSourceServiceName() == null && flowValue.getSourceServiceName() != null) {
            aggregate.setSourceServiceName(flowValue.getSourceServiceName());
        }

        if (aggregate.getSourceSystem() == null && flowValue.getSourceSystem() != null) {
            aggregate.setSourceSystem(flowValue.getSourceSystem());
        }

        if (aggregate.getDeviceMacAddress() == null && flowValue.getDeviceMacAddress() != null) {
            aggregate.setDeviceMacAddress(flowValue.getDeviceMacAddress());
        }

        if (aggregate.getDeviceName() == null && flowValue.getDeviceName() != null) {
            aggregate.setDeviceName(flowValue.getDeviceName());
        }

        if (aggregate.getDeviceOutboundInterface() == null && flowValue.getDeviceOutboundInterface() != null) {
            aggregate.setDeviceOutboundInterface(flowValue.getDeviceOutboundInterface());
        }

        if (aggregate.getDeviceProduct() == null && flowValue.getDeviceProduct() != null) {
            aggregate.setDeviceProduct(flowValue.getDeviceProduct());
        }

        if (aggregate.getDeviceTranslatedAddress() == null && flowValue.getDeviceTranslatedAddress() != null) {
            aggregate.setDeviceTranslatedAddress(flowValue.getDeviceTranslatedAddress());
        }

        if (aggregate.getDeviceVersion() == null && flowValue.getDeviceVersion() != null) {
            aggregate.setDeviceVersion(flowValue.getDeviceVersion());
        }

        if (aggregate.getDeviceTimeZone() == null && flowValue.getDeviceTimeZone() != null) {
            aggregate.setDeviceTimeZone(flowValue.getDeviceTimeZone());
        }

        if (aggregate.getDeviceExternalId() == null && flowValue.getDeviceExternalId() != null) {
            aggregate.setDeviceExternalId(flowValue.getDeviceExternalId());
        }

        if (aggregate.getDeviceCustomerNumber3() == null && flowValue.getDeviceCustomerNumber3() != null) {
            aggregate.setDeviceCustomerNumber3(flowValue.getDeviceCustomerNumber3());
        }

        if (aggregate.getReceiptTime() == null && flowValue.getReceiptTime() != null) {
            aggregate.setReceiptTime(flowValue.getReceiptTime());
        }

        if (aggregate.getActivity() == null && flowValue.getActivity() != null) {
            aggregate.setActivity(flowValue.getActivity());
        }

        if (aggregate.getAdditionalExtensions() == null && flowValue.getAdditionalExtensions() != null) {
            aggregate.setAdditionalExtensions(flowValue.getAdditionalExtensions());
        }

        if (aggregate.getSourceZone() == null && flowValue.getSourceZone() != null) {
            aggregate.setSourceZone(flowValue.getSourceZone());
        }

        if (aggregate.getDestinationZone() == null && flowValue.getDestinationZone() != null) {
            aggregate.setDestinationZone(flowValue.getDestinationZone());
        }

        if (aggregate.getRequestUrl() == null && flowValue.getRequestUrl() != null) {
            aggregate.setRequestUrl(flowValue.getRequestUrl());
        }

        if (aggregate.getSrcCloudTags() == null && flowValue.getSrcCloudTags() != null) {
            aggregate.setSrcCloudTags(flowValue.getSrcCloudTags());
        }

        if (aggregate.getDestCloudTags() == null && flowValue.getDestCloudTags() != null) {
            aggregate.setDestCloudTags(flowValue.getDestCloudTags());
        }

        if (aggregate.getFlowCount() == null && flowValue.getFlowCount() != null) {
            aggregate.setFlowCount(flowValue.getFlowCount());
        }

        if (aggregate.getPacketsSent() == null && flowValue.getPacketsSent() != null) {
            aggregate.setPacketsSent(flowValue.getPacketsSent());
        }

        if (aggregate.getPacketsReceived() == null && flowValue.getPacketsReceived() != null) {
            aggregate.setPacketsReceived(flowValue.getPacketsReceived());
        }

        if (aggregate.getTrafficStatus() == null && flowValue.getTrafficStatus() != null) {
            aggregate.setTrafficStatus(flowValue.getTrafficStatus());
        }

        if(aggregate.getDeviceVendor() == null && flowValue.getDeviceVendor() != null) {
            aggregate.setDeviceVendor(flowValue.getDeviceVendor());
        }

        if(aggregate.getDestinationUserPrivileges() == null && flowValue.getDestinationUserPrivileges() != null) {
            aggregate.setDestinationUserPrivileges(flowValue.getDestinationUserPrivileges());
        }

        if(aggregate.getPacketsReceived() == null && flowValue.getPacketsReceived() != null) {
            aggregate.setPacketsReceived(flowValue.getPacketsReceived());
        }

        if(aggregate.getPacketsSent() == null && flowValue.getPacketsSent() != null) {
            aggregate.setPacketsSent(flowValue.getPacketsSent());
        }

        if(aggregate.getOrgId() == null && flowValue.getOrgId() != null) {
            aggregate.setOrgId(flowValue.getOrgId());
        }

        if(aggregate.getFlowlinkInstallationId() == null && flowValue.getFlowlinkInstallationId() != null) {
            aggregate.setFlowlinkInstallationId(flowValue.getFlowlinkInstallationId());
        }

        if(aggregate.getPceHost() == null && flowValue.getPceHost() != null) {
            aggregate.setPceHost(flowValue.getPceHost());
        }

        if(aggregate.getIcmpType() == null && flowValue.getIcmpType() != null) {
            aggregate.setIcmpType(flowValue.getIcmpType());
        }

        if(aggregate.getIcmpCode() == null && flowValue.getIcmpCode() != null) {
            aggregate.setIcmpCode(flowValue.getIcmpCode());
        }

        if(aggregate.getSrcNetworkMask() == null && flowValue.getSrcNetworkMask() != null) {
            aggregate.setSrcNetworkMask(flowValue.getSrcNetworkMask());
        }

        if(aggregate.getDestNetworkMask() == null && flowValue.getDestNetworkMask() != null) {
            aggregate.setDestNetworkMask(flowValue.getDestNetworkMask());
        }
    }
}
