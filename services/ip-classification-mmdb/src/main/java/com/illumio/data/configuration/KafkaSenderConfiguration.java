package com.illumio.data.configuration;

import lombok.RequiredArgsConstructor;

import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderOptions;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Configuration
@RequiredArgsConstructor
public class KafkaSenderConfiguration {
    private final IpClassificationMmdbConfig ipClassificationMmdbConfig;

    @Bean
    public List<KafkaSender<String, String>> kafkaSenders() {
        Map<String, Object> producerProps = ipClassificationMmdbConfig.createProducerProps();
        List<KafkaSender<String, String>> senders = new ArrayList<>();
        for (int i = 0; i < ipClassificationMmdbConfig.getKafkaSenderConfig().getNumProducers(); i++) {
            SenderOptions<String, String> senderOptions =
                    SenderOptions.<String, String>create(producerProps)
                            .maxInFlight(1024);
            senders.add(KafkaSender.create(senderOptions));
        }
        return senders;
    }


}
