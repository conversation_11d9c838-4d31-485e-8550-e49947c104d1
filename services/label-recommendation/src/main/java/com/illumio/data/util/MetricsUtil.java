package com.illumio.data.util;

import io.opentelemetry.api.common.Attributes;
import io.opentelemetry.api.metrics.LongCounter;
import io.opentelemetry.api.metrics.Meter;
import org.springframework.stereotype.Component;

@Component
public class MetricsUtil {
    public static final String METRIC_RECORDS_INCOMING = "records_incoming";
    public static final String METRIC_RECORDS_OUTGOING = "records_outgoing";
    public static final String METRIC_RECORDS_ENRICHED = "records_enriched";
    public static final String METRIC_RECORDS_UNENRICHED_MISSING_LABELS = "records_unenriched_missing_labels";
    private final LongCounter recordsEnriched;
    private final LongCounter recordsUnenrichedMissingLabels;
    private final LongCounter incomingRecords;
    private final LongCounter malformedRecords;
    private final LongCounter outgoingRecords;
    private final LongCounter acceptedTenantRecords;
    private final LongCounter processedTenantRecords;

    public MetricsUtil(Meter meter) {
        this.recordsEnriched = this.createCounter(meter,
                METRIC_RECORDS_ENRICHED,
                "No of flow records processed with enrichment");
        this.recordsUnenrichedMissingLabels = this.createCounter(meter,
                METRIC_RECORDS_UNENRICHED_MISSING_LABELS,
                "No of flow records processed without enrichment due to missing labels");

        this.incomingRecords = this.createCounter(meter,
                METRIC_RECORDS_INCOMING,
                "No of incoming flow records");
        this.malformedRecords = this.createCounter(meter,
                FlowDataValidator.INSIGHTS_INCOMING_WRONG_EVENTS,
                "No of incoming flow records with malformed data");
        this.outgoingRecords = this.createCounter(meter,
                METRIC_RECORDS_OUTGOING,
                "No of outgoing flow records");
        this.acceptedTenantRecords = this.createCounter(meter,
                FlowDataValidator.INSIGHTS_INCOMING_EVENTS,
                "No of accepted flow records per tenant");
        this.processedTenantRecords = this.createCounter(meter,
                FlowDataValidator.INSIGHTS_OUTGOING_EVENTS,
                "No of processed flow records per tenant");
    }

    public void incrementRecordsIncoming() {
        this.incomingRecords.add(1L);
    }

    public void incrementRecordsOutgoing() {
        this.outgoingRecords.add(1L);
    }

    public void incrementRecordsWithErrors(Attributes attributes) {
        this.malformedRecords.add(1L, attributes);
    }

    public void incrementRecordsEnriched() {
        this.recordsEnriched.add(1L);
    }

    public void incrementRecordsUnenrichedMissingLabels() {
        this.recordsUnenrichedMissingLabels.add(1L);
    }

    public void incrementAcceptedRecordPerTenant(Attributes attributes) {
        this.acceptedTenantRecords.add(1L, attributes);
    }

    public void incrementOutgoingRecordPerTenant(Attributes attributes) {
        this.processedTenantRecords.add(1L, attributes);
    }

    private LongCounter createCounter(Meter meter, String name, String description) {
        return meter.counterBuilder(name).setDescription(description).build();
    }
}
