package com.illumio.data;

import com.illumio.data.configuration.InsightsServiceConfiguration;
import com.illumio.data.model.DerivedMetadata;
import com.illumio.data.model.RequestContext;
import com.illumio.data.model.RequestPayload;
import com.illumio.data.model.constants.Metadata;
import com.illumio.data.model.constants.RequestMetadata;
import com.illumio.data.builder.ResponseBuilder;
import com.illumio.data.model.constants.Template;
import com.illumio.data.model.constants.WidgetId;
import com.illumio.data.repository.KustoQueryRepository;
import com.illumio.data.service.MetricRecordService;
import com.illumio.data.utils.MetadataHelper;
import com.microsoft.azure.kusto.data.Client;
import com.microsoft.azure.kusto.data.KustoOperationResult;
import com.microsoft.azure.kusto.data.KustoResultSetTable;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.util.MultiValueMap;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Scheduler;
import reactor.core.scheduler.Schedulers;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Base64;
import java.util.Optional;

@Component
@Slf4j
public class KustoQueryClient {
    private final Client kustoClient;
    private final Client kustoInsightsClient;

    private static final Scheduler KUSTO_SCHEDULER =
            Schedulers.newBoundedElastic(30, 1000, "kusto-scheduler", 60, true);
    private static final Scheduler KUSTO_SCHEDULER_MSFT =
            Schedulers.newBoundedElastic(15, 500, "kusto-scheduler-msft", 60, true);

    private final String kustoDatabase;
    private final String kustoInsDatabase;
    private final KustoQueryRepository kustoQueryRepository;
    private final MeterRegistry meterRegistry;
    private final MetricRecordService metricRecordService;
    private static final String INSIGHTS_PREFIX = "Insights_";
    private static final String DAILY_SUFFIX = "_Daily";

    public KustoQueryClient(@Qualifier("kustoClient") Client kustoClient,
                            @Qualifier("kustoInsightsClient") Client kustoInsightsClient,
                            InsightsServiceConfiguration inventoryConfig,
                            KustoQueryRepository kustoQueryRepository,
                            MeterRegistry meterRegistry,
                            MetricRecordService metricRecordService) {
        this.kustoClient = kustoClient;
        this.kustoInsightsClient = kustoInsightsClient;
        this.kustoDatabase = inventoryConfig.getKustoConfig().getDatabase();
        this.kustoInsDatabase = inventoryConfig.getKustoInsightsConfig().getDatabase();
        this.kustoQueryRepository = kustoQueryRepository;
        this.meterRegistry = meterRegistry;
        this.metricRecordService = metricRecordService;
    }

    public <R> Mono<R> getResults(Metadata requestMetadata, RequestContext requestContext, ResponseBuilder<R> responseBuilder) {
        String logPrefix = "page ID: %s, widget ID: %s, request type: %s".formatted(requestMetadata.getPageId(), requestMetadata.getWidgetId(), requestMetadata.getRequestType());

        Timer.Sample queryTimer = Timer.start(meterRegistry);
        log.debug("GetResults Kusto Query for {}", logPrefix);

        if(requestMetadata.getWidgetId().equalsIgnoreCase(WidgetId.RESOURCE_INSIGHTS))
            return getAggregatedResults(MetadataHelper.toRequestMetadataEnum(requestMetadata), requestContext.getHeaders(), requestContext.getQueryParams(), requestContext.getRequestPayload(), requestContext.getTenantId() , responseBuilder, queryTimer);

        // TODO: There's a bug in Spark code - Weekly data is messed up.
        // For now, we're just sending everything to daily tables unless its hourly
        DerivedMetadata m = requestContext.getDerivedMetadata().get();
        if(m.getCurrentTimeFrameTable().contains("_Hourly") ){
            log.debug("GetResults Kusto Query for Hourly {}", logPrefix);

                    return runQueryAndBuildResponse(requestContext, requestMetadata, responseBuilder, queryTimer, false)
                .switchIfEmpty(
                        Mono.defer(() -> {
                            log.info("Primary result empty. Falling back to daily aggregated table");
                            // fallback to daily table if data is empty
                            String fallbackTable = INSIGHTS_PREFIX + requestMetadata.getTableName() + DAILY_SUFFIX;
                            Optional<DerivedMetadata> fallbackMetadata = requestContext.getDerivedMetadata()
                                    .map(derivedMetadata -> derivedMetadata.fallbackToDaily(fallbackTable));
                            RequestContext fallbackContext = requestContext.toBuilder()
                                                                .derivedMetadata(fallbackMetadata)
                                                                .build();
                            return runQueryAndBuildResponse(fallbackContext, requestMetadata, responseBuilder, queryTimer, true);
                        })
                );
        } else {
            log.debug("GetResults Kusto Query for Daily {}", logPrefix);
            String fallbackTable = INSIGHTS_PREFIX + requestMetadata.getTableName() + DAILY_SUFFIX;
            Optional<DerivedMetadata> fallbackMetadata = requestContext.getDerivedMetadata()
                    .map(derivedMetadata -> derivedMetadata.fallbackToDaily(fallbackTable));
            RequestContext fallbackContext = requestContext.toBuilder()
                    .derivedMetadata(fallbackMetadata)
                    .build();
            return runQueryAndBuildResponse(fallbackContext, requestMetadata, responseBuilder, queryTimer, true);
        }
    }

    private <R> Mono<R> runQueryAndBuildResponse(RequestContext requestContext,
                                                 Metadata requestMetadata,
                                                 ResponseBuilder<R> responseBuilder,
                                                 Timer.Sample queryTimer, Boolean isFallBack) {

        String logPrefix = "page ID: %s, widget ID: %s, request type: %s".formatted(requestMetadata.getPageId(), requestMetadata.getWidgetId(), requestMetadata.getRequestType());
        log.debug("Scheduling Kusto Query for {}", logPrefix);

        return createQuery(requestContext, requestMetadata)
                .doOnNext(
                        query ->
                                log.info("Running Kusto Query for {}, query: {}", logPrefix, query))
                .flatMap(
                        query -> {
                            metricRecordService.recordQueryLatency(
                                meterRegistry,
                                queryTimer,
                                requestMetadata,
                                requestContext.getTenantId().get(),
                                "BeforeQuery");

                            Timer.Sample queryTimerKusto = Timer.start(meterRegistry);

                            return Mono.fromCallable(() -> executeKustoInsightsQuery(query))
                                    .subscribeOn(KUSTO_SCHEDULER)
                                    .publishOn(Schedulers.boundedElastic())
                                    .timeout(Duration.ofSeconds(20))
                                    .retry(1)
                                    .flatMap(
                                            kustoResultSetTable -> {
                                                log.debug("Done Kusto Query for {}", logPrefix);

                                                if (kustoResultSetTable.count() == 0 && !isFallBack)
                                                    return Mono.empty();
                                                metricRecordService.recordQueryLatency(
                                                        meterRegistry,
                                                        queryTimerKusto,
                                                        requestMetadata,
                                                        requestContext.getTenantId().get(),
                                                        "success");
                                                return Mono.fromCallable(
                                                        () ->
                                                                responseBuilder.buildResponse(
                                                                        kustoResultSetTable,
                                                                        requestContext,
                                                                        requestMetadata));
                                            });
                        })
                .onErrorResume(
                        error -> {
                            log.error("Error Kusto Query for {} ", logPrefix, error);
                            return Mono.error(
                                    new RuntimeException("Kusto Database can not process request"));
                        });
    }

    public <R> Mono<R> getAggregatedResults(RequestMetadata metadata,
                                            Optional<MultiValueMap<String, String>> headers,
                                            Optional<MultiValueMap<String, String>> params,
                                            Optional<RequestPayload> payload,
                                            Optional<String> tenantId,
                                            ResponseBuilder<R> responseBuilder,
                                            Timer.Sample queryTimer) {
        // Decode tenant IDs and start time
        String azEnc = headers.flatMap(h -> Optional.ofNullable(h.getFirst("x-azure-tenant-id"))).orElse("");
        String ilEnc = headers.flatMap(h -> Optional.ofNullable(h.getFirst("x-illumio-tenant-id"))).orElse("");
        String startTime = params.flatMap(p -> Optional.ofNullable(p.getFirst("after"))).orElse(null);
        String azId = new String(Base64.getDecoder().decode(azEnc));
        String ilId;
        try {
            ilId = new String(Base64.getDecoder().decode(ilEnc));
            log.info("Tenant id decoded iqs: {}", ilId);
        }catch (IllegalArgumentException e) {
            ilId = ilEnc;
            log.info("Tenant Id is not Base64 Encoded: {}", ilId);
        }

        // Build raw query strings
        String q1 = kustoQueryRepository.getRiskyResourceQueryString(azId, ilId, startTime);
        String q2 = kustoQueryRepository.getMaliciousResourceQueryString(azId, ilId, startTime);
        String q3 = kustoQueryRepository.getMLLabelResourceQueryString(azId, ilId, startTime);
        String q4 = kustoQueryRepository.getLLMResourceQueryString(azId, ilId, startTime);
        String q5 = kustoQueryRepository.getExternalTransferResourceQueryString(azId, ilId, startTime);
        log.info("Kusto Query for Risky Insights : {}", q1);
        log.info("Kusto Query for Malicious IP Insights : {}", q2);
        log.info("Kusto Query for ML Label Insights : {}", q3);
        log.info("Kusto Query for LLM Access Insights : {}", q4);
        log.info("Kusto Query for External Data Transfer Insights : {}", q5);

        // Sequentially execute three queries, collecting raw tables
        Flux<KustoOperationResult> queries = Flux.concat(
                Mono.fromCallable(() -> executeKustoQuery(q1))
                        .subscribeOn(KUSTO_SCHEDULER_MSFT),
                Mono.fromCallable(() -> executeKustoQuery(q2))
                        .subscribeOn(KUSTO_SCHEDULER_MSFT),
                Mono.fromCallable(() -> executeKustoQuery(q3))
                        .subscribeOn(KUSTO_SCHEDULER_MSFT),
                Mono.fromCallable(() -> executeKustoQuery(q4))
                        .subscribeOn(KUSTO_SCHEDULER_MSFT),
                Mono.fromCallable(() -> executeKustoQuery(q5))
                        .subscribeOn(KUSTO_SCHEDULER_MSFT)
        );

        return queries
                .map(KustoOperationResult::getPrimaryResults)
                .collectList()
                .map(tables -> {
                    metricRecordService.recordQueryLatency(
                            meterRegistry, queryTimer,
                            MetadataHelper.toMetadataObject(RequestMetadata.RESOURCE_INSIGHTS, Template.TYPE_ONE),
                            tenantId.orElse("unknown"), "success");
                    return responseBuilder.buildAggregatedResponse(tables, params);
                });
    }
    private Mono<String> createQuery(RequestContext requestContext, Metadata metadata) {
        return Mono.fromSupplier(() -> kustoQueryRepository.getQueryString(requestContext, metadata));

    }

    @SneakyThrows
    public KustoOperationResult executeKustoQuery(String query) {
        return kustoClient.execute(kustoDatabase, query);
    }

    @SneakyThrows
    public KustoResultSetTable executeKustoInsightsQuery(String query) {
        KustoOperationResult kustoOperationResult = kustoInsightsClient.execute(kustoInsDatabase, query);
        return kustoOperationResult.getPrimaryResults();
    }
}
