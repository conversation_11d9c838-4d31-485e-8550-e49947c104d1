CREATE TABLE IF NOT EXISTS tickets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id CHARACTER VARYING NOT NULL,
    created_by CHARACTER VARYING NOT NULL,
    source_app CHARACTER VARYING NOT NULL,
    report_id CHARACTER VARYING,
    integration CHARACTER VARYING NOT NULL,
    external_ticket_number CHARACTER VARYING NOT NULL,
    external_ticket_link CHARACTER VARYING,
    ticket_metadata JSONB,
    ticket_state CHARACTER VARYING NOT NULL,
    ticket_created_at TIMESTAMPTZ,
    ticket_updated_at TIMESTAMPTZ,
    CONSTRAINT uk_tickets UNIQUE (tenant_id, integration, external_ticket_number)
);
