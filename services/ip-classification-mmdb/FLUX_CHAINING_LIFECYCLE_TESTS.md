# Flux Chaining Lifecycle Tests

## Overview

This document describes the comprehensive unit tests created to demonstrate and verify different Flux chaining scenarios and their lifecycle behavior, particularly focusing on error handling and recreation patterns.

## Test Cases

### Case 1: FluxA Chained with FluxB - Error Propagation with Recreation

**Scenario**: FluxA is directly chained with FluxB. When FluxA encounters an error, both FluxA and FluxB get recreated.

**Key Behavior**:
- FluxA emits values: A1, A2, A3, then errors
- FluxB processes these values: B-processed-A1, B-processed-A2, B-processed-A3
- When FluxA errors, the entire chain is recreated
- Both FluxA and FluxB are recreated on retry (3 times total)

**Proof of Recreation**:
- FluxA creation count: 3
- FluxB creation count: 3
- Both fluxes are recreated together due to tight coupling

### Case 2: Separated Lifecycle with Error Propagation

**Scenario**: FluxA and FluxB have conceptually separated lifecycles, but FluxB errors still propagate to FluxA, causing both to be recreated.

**Key Behavior**:
- FluxA emits continuous values: A0, A1, A2, A3, A4, A5...
- FluxB takes only 3 items then errors
- Error propagates back to FluxA, causing both to be recreated
- Process repeats with new instances

**Proof of Recreation**:
- FluxA creation count: 3 (recreated due to error propagation)
- FluxB creation count: 3 (recreated due to its own errors)
- Error propagation causes full chain recreation

### Case 3: Separated Lifecycle without Error Propagation

**Scenario**: FluxA and FluxB have truly separated lifecycles. FluxB errors are isolated and handled without affecting FluxA.

**Key Behavior**:
- FluxA runs independently and continuously
- FluxB errors are caught and handled with `onErrorResume`
- FluxB can be recreated without affecting FluxA
- Error isolation prevents unnecessary FluxA recreation

**Proof of Isolation**:
- FluxA creation count: 2 (minimal recreation due to share() behavior)
- FluxB creation count: 1 (error handled, no recreation needed)
- Errors are isolated and don't propagate

### Case 4: Selective Error Handling

**Scenario**: Based on Case 3, FluxB emits different exceptions (ExceptionB1, ExceptionB2, ExceptionB1). FluxA only catches ExceptionB1 in onErrorResume, but recreates when encountering ExceptionB2.

**Key Behavior**:
- Run 1: FluxB emits ExceptionB1 → FluxA catches and handles gracefully → completes
- Run 2: FluxB emits ExceptionB2 → FluxA doesn't catch → error propagates → recreation
- Run 3: FluxB emits ExceptionB1 → FluxA catches and handles gracefully → completes

**Proof of Selective Handling**:
- FluxA creation count: 3 (recreated when ExceptionB2 occurs)
- FluxB creation count: 3 (all runs)
- ExceptionB1 handled gracefully, ExceptionB2 causes recreation

## Additional Verification Tests

### Flux Recreation Proof with Subscription Tracking

**Purpose**: Provides concrete evidence of Flux recreation by tracking subscriptions and disposals.

**Verification**:
- Tracks subscription count: 3
- Tracks disposal count: 3
- Proves that each retry creates a new Flux instance

### Flux Instance Identity Proof

**Purpose**: Demonstrates that recreated Fluxes are actually different object instances.

**Verification**:
- Creates unique instance IDs: [1, 2, 3]
- Proves each recreation produces a distinct object
- Validates that recreation isn't just resubscription

### Recreation vs Non-Recreation Comparison

**Purpose**: Shows the clear difference between scenarios that recreate Fluxes and those that don't.

**Verification**:
- Recreating flux: 2 instances created
- Non-recreating flux: 1 instance created
- Demonstrates the impact of different error handling strategies

## Key Insights

### 1. Error Propagation Patterns
- **Tight Coupling**: Errors in any part of the chain cause full recreation
- **Loose Coupling with Propagation**: Conceptually separate but errors still propagate
- **True Isolation**: Errors are contained and handled locally

### 2. Recreation Evidence
- **Creation Counters**: Track how many times each Flux factory is called
- **Subscription Tracking**: Monitor subscription/disposal lifecycle events
- **Instance Identity**: Verify that new objects are actually created

### 3. Performance Implications
- **Case 1 & 2**: Higher resource usage due to full chain recreation
- **Case 3**: More efficient as only affected components are recreated
- **Error Isolation**: Reduces unnecessary work and improves stability

## Test Execution Results

```
✅ Case 1: FluxA error caused FluxB recreation 3 times
✅ Case 2: FluxB error propagated and caused FluxA recreation 3 times
✅ Case 3: FluxB created 1 times with FluxA created 2 times (errors isolated and handled)
✅ Case 4: Selective error handling - FluxA created 3 times, FluxB created 3 times
    - ExceptionB1 (runs 1,3): handled gracefully, no recreation
    - ExceptionB2 (run 2): caused FluxA recreation
✅ Instance Identity Proof: Created 3 unique instances with IDs: [1, 2, 3]
✅ Recreation vs Non-Recreation: Recreating flux created 2 times, Non-recreating flux created 1 times
✅ Recreation Proof: 3 creations, 3 subscriptions, 3 disposals
```

## Usage in Real Applications

These patterns are particularly relevant for:

1. **Kafka Consumer/Producer Chains**: Where consumer errors shouldn't necessarily recreate producers
2. **Database Connection Pools**: Where connection errors shouldn't recreate the entire pool
3. **Microservice Communication**: Where downstream service errors shouldn't recreate upstream components
4. **Stream Processing Pipelines**: Where different stages may have different error recovery strategies

## Best Practices

1. **Use Error Isolation**: Implement `onErrorResume` to contain errors locally
2. **Monitor Recreation**: Track creation counts to identify unnecessary recreations
3. **Separate Concerns**: Design independent lifecycles for loosely coupled components
4. **Test Thoroughly**: Verify error propagation behavior with comprehensive tests
